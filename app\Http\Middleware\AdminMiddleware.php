<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if ($request->session()->has('logado')) {
            $usuario = $request->session()->get('logado');
            if (isset($usuario['admin']) && $usuario['admin'] == 1) {
                return $next($request);
            }
        }

        return redirect()->route('login')->with('message', 'Acesso não autorizado.');
    }
}
