$(document).ready(function () {
    if ($(".btn-danger").length > 0) {
        $(".btn-danger").click(function () {
            return confirm("Tem certeza que deseja excluir?");
        });
    }
    // Auto-preenchimento
    if ($("#dt_emissao").length > 0) {
        $("#dt_emissao").on("change blur", function () {
            $("#dt_emissao_cli").val($(this).val());
            verificar();
        });
        $("#dt_prev_entrega").on("change blur", function () {
            $("#dt_prev_entrega_cli").val($(this).val());
            verificar();
        });

        // MASCARA PARA DATA - apenas para campos que não são HTML5 date
        $(".data:not([type='date'])").mask("99/99/9999");

        $(".dinheiro").maskMoney({
            symbol: "",
            showSymbol: true,
            thousands: ".",
            decimal: ",",
            symbolStay: true,
        });
        //$(".cil").maskMoney({symbol:'-', showSymbol:true, thousands:'.', decimal:',', symbolStay: true});

        // VALOR TOTAL
        $("#valor_total").blur(function () {
            calcula1();
        });
        $("#pago").blur(function () {
            calcula1();
        });
        $("#a_pagar").blur(function () {
            calcula1();
        });
    }
});
function calcula1() {
    var valor_total = $("#valor_total")
        .val()
        .replace(".", "")
        .replace(",", ".");
    var pago = $("#pago").val().replace(".", "").replace(",", ".");
    var resultado = parseFloat(valor_total - pago);

    var valor = (resultado.toFixed(2) + "").replace(".", ",");

    $("#a_pagar").val(valor);
}
function verificar() {
    $("#dt_emissao").val();
}

// Adiciona o cálculo para os campos Perto ao alterar Longe ou Adição
// A função só será ativada se o campo 'adicao' existir na página.
if (document.querySelector('[name="adicao"]')) {
    function calcularPerto() {
        const adicaoEl = document.querySelector('[name="adicao"]');
        const adicaoVal = adicaoEl.value.trim();

        // Itera para olho direito (od) e olho esquerdo (oe)
        ["od", "oe"].forEach((olho) => {
            // Pega os elementos de Longe
            const longeEsfValEl = document.querySelector(
                `[name="longe[${olho}][esf]"]`
            );
            const longeEsfValRaw = longeEsfValEl.value.trim();

            // *** VERIFICAÇÃO PRINCIPAL ***
            // Só executa o cálculo se Adição e Longe ESF tiverem valores
            if (adicaoVal !== "" && longeEsfValRaw !== "") {
                const adicao = parseFloat(adicaoVal.replace(",", ".")) || 0;

                const longeEsfSig = document.querySelector(
                    `[name="longe[${olho}][esf_sig]"]`
                ).value;
                const longeEsfVal =
                    parseFloat(longeEsfValRaw.replace(",", ".")) || 0;
                const longeCilVal = document.querySelector(
                    `[name="longe[${olho}][cil]"]`
                ).value;
                const longeEixoVal = document.querySelector(
                    `[name="longe[${olho}][eixo]"]`
                ).value;

                // Pega os elementos de Perto
                const pertoEsfSigEl = document.querySelector(
                    `[name="perto[${olho}][esf_sig]"]`
                );
                const pertoEsfValEl = document.querySelector(
                    `[name="perto[${olho}][esf]"]`
                );
                const pertoCilValEl = document.querySelector(
                    `[name="perto[${olho}][cil]"]`
                );
                const pertoEixoValEl = document.querySelector(
                    `[name="perto[${olho}][eixo]"]`
                );

                // Combina sinal e valor do esférico de Longe
                const longeEsfericoFinal =
                    longeEsfSig === "-" ? -longeEsfVal : longeEsfVal;

                // Realiza a soma
                const pertoEsfericoResultado = longeEsfericoFinal + adicao;

                // Define o sinal e o valor para Perto
                if (pertoEsfericoResultado >= 0) {
                    pertoEsfSigEl.value = "+";
                    pertoEsfValEl.value = pertoEsfericoResultado
                        .toFixed(2)
                        .replace(".", ",");
                } else {
                    pertoEsfSigEl.value = "-";
                    pertoEsfValEl.value = Math.abs(pertoEsfericoResultado)
                        .toFixed(2)
                        .replace(".", ",");
                }

                // Copia CIL e EIXO
                pertoCilValEl.value = longeCilVal;
                pertoEixoValEl.value = longeEixoVal;
            }
            // Se as condições não forem atendidas, nenhum preenchimento automático acontece.
        });
    }

    // Lista de todos os campos que devem acionar o cálculo
    const camposGatilho = [
        '[name="adicao"]',
        '[name="longe[od][esf_sig]"]',
        '[name="longe[od][esf]"]',
        '[name="longe[od][cil]"]',
        '[name="longe[od][eixo]"]',
        '[name="longe[oe][esf_sig]"]',
        '[name="longe[oe][esf]"]',
        '[name="longe[oe][cil]"]',
        '[name="longe[oe][eixo]"]',
    ];

    // Adiciona o "escutador" de evento a cada campo
    camposGatilho.forEach((seletor) => {
        const elemento = document.querySelector(seletor);
        if (elemento) {
            elemento.addEventListener("input", calcularPerto);
            elemento.addEventListener("change", calcularPerto); // Para os selects
        }
    });
}
function calcularPrevisaoEntrega(diasUteis) {
        const dataEmissao = document.getElementById('dt_emissao').value;

        if (!dataEmissao) {
            alert('Por favor, preencha a Data de Emissão primeiro.');
            return;
        }

        const data = new Date(dataEmissao);
        let diasAdicionados = 0;

        while (diasAdicionados < diasUteis) {
            data.setDate(data.getDate() + 1);

            // Verifica se não é sábado (6) nem domingo (0)
            if (data.getDay() !== 0 && data.getDay() !== 6) {
                diasAdicionados++;
            }
        }

        // Formatar a data para o formato YYYY-MM-DD
        const ano = data.getFullYear();
        const mes = String(data.getMonth() + 1).padStart(2, '0');
        const dia = String(data.getDate()).padStart(2, '0');

        document.getElementById('dt_prev_entrega').value = `${ano}-${mes}-${dia}`;
    }