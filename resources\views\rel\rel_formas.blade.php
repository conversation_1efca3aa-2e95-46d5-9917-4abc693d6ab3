@extends('template.master')
@section('sidebar')


@stop
@section('content')
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

    <script type="text/javascript">
        google.charts.load('current', {'packages':['corechart', 'bar']});
        google.charts.setOnLoadCallback(drawChart);
        function drawChart() {
            var data = google.visualization.arrayToDataTable([
                ['Meses', 'Dinheiro', 'Visa', 'Cheque', 'Master', "Outros", "Não informado"],
                <?php
                //print_r($dados["rel_dados_formas"]);

                foreach($dados["rel_dados_formas"] as $dado){
                    if( $dado->Forma != null ){
                        $mes[$dado->Mes."-".$dado->Ano][$dado->Forma] = $dado->Valor;
                    }else{
                        $mes[$dado->Mes."-".$dado->Ano]["n"] = $dado->Valor;
                    }
                }
                foreach($mes as $key=>$value){
                    $string = "['$key'";

                    isset($value["d"]) ? $string.= ",".$value["d"] : $string.=", 0";
                    isset($value["v"]) ? $string.= ",".$value["v"] : $string.=", 0";
                    isset($value["c"]) ? $string.= ",".$value["c"] : $string.=", 0";
                    isset($value["m"]) ? $string.= ",".$value["m"] : $string.=", 0";
                    isset($value["o"]) ? $string.= ",".$value["o"] : $string.=", 0";
                    isset($value["n"]) ? $string.= ",".$value["n"] : $string.=", 0";


                    $string .= "],";
                    echo $string;
                }
                ?>
                //['Janeiro', 1000, 400, 0,0,0],
                //['2015', 1170, 460, 0,0,0],
            ]);

            var options = {
                chart: {
                    title: 'Performace da empresa',
                    subtitle: 'Formas de pagamento X Mês',
                }
            };

            var chart = new google.charts.Bar(document.getElementById('columnchart_material'));

            chart.draw(data, options);
        }
    </script>


<div class="panel panel-default">
    <div class="panel-heading">Informações</div>
    <div class="panel-body">
        <div class="col-sm-12">
            <form>
                <div class="col-sm-3"><input name="data_inicio" class="form-control col-sm-2" type="date" /></div>
                <div class="col-sm-3"><input name="data_fim" class="form-control col-sm-2" type="date" /></div>
                <div class="col-sm-2"><input type="submit" class="btn btn-primary" value="Gerar" /></div>
            </form>
        </div>
        <div class="col-sm-12">


            <h3>Valores por Formas de pagamento</h3>
            <div id="columnchart_material" style="width: 900px; height: 500px;"></div>
        </div>
    </div>
    </div>
</div>
@stop
