@extends('template.master')
@section('sidebar')


@stop
@section('content')


<div class="filtros-container">
    <h4 class="filtros-titulo">
        <i class="fa fa-search"></i>
        Filtros de Pesquisa
    </h4>

    <form method="GET" action="{{ route('index') }}">
        <div class="form-row-custom">
            <div class="form-group-filtro">
                <label for="search">
                    <i class="fa fa-user"></i>
                    Cliente/Código
                </label>
                <input type="text"
                    class="form-control-filtro"
                    id="search"
                    name="search"
                    value="{{ Request::input('search')}}"
                    placeholder="Nome do cliente ou código da O.S.">
            </div>

            <div class="form-group-filtro">
                <label for="id_filial">
                    <i class="fa fa-building"></i>
                    Loja
                </label>
                <select class="form-control-filtro" id="id_filial" name="id_filial">
                    <option value="">Todas as lojas</option>
                    @foreach($filiais as $id => $nome)
                    <option value="{{ $id }}" {{ Request::input('id_filial') == $id ? 'selected' : '' }}>
                        {{ $nome }}
                    </option>
                    @endforeach
                </select>
            </div>

            <div class="form-group-filtro">
                <label for="vendedor">
                    <i class="fa fa-id-badge"></i>
                    Vendedor
                </label>
                <input type="text"
                    class="form-control-filtro"
                    id="vendedor"
                    name="vendedor"
                    value="{{ Request::input('vendedor')}}"
                    placeholder="Nome do vendedor">
            </div>

            <div class="form-group-filtro">
                <label>
                    <i class="fa fa-calendar"></i>
                    Período (Dt. Emissão)
                </label>
                <div class="periodo-grupo">
                    <input type="date"
                        class="form-control-filtro"
                        name="data_inicio"
                        value="{{ Request::input('data_inicio') ?? date('Y-m-d') }}">
                    <span class="periodo-ate">até</span>
                    <input type="date"
                        class="form-control-filtro"
                        name="data_fim"
                        value="{{ Request::input('data_fim') ?? date('Y-m-d') }}">
                </div>
                <div class="atalhos-grupo">
                    <button type="button" class="btn-atalho" onclick="preencher11Meses()">+11 meses</button>
                    <button type="button" class="btn-atalho" onclick="preencher1Ano()">+1 ano</button>
                </div>
            </div>

            <div class="form-group-filtro">
                <label>
                    <i class="fa fa-calendar-check-o"></i>
                    Dt. Prev. Entrega:
                </label>
                <div class="periodo-grupo">
                    <input type="date"
                        class="form-control-filtro"
                        name="dt_prev_entrega_inicio"
                        value="{{ Request::input('dt_prev_entrega_inicio') }}">
                    <span class="periodo-ate">até</span>
                    <input type="date"
                        class="form-control-filtro"
                        name="dt_prev_entrega_fim"
                        value="{{ Request::input('dt_prev_entrega_fim') }}">
                </div>
                <div class="atalhos-grupo">
                    <button type="button" class="btn-atalho" onclick="preencherHoje()">Hoje</button>
                    <button type="button" class="btn-atalho" onclick="preencherSemana()">Semana</button>
                    <button type="button" class="btn-atalho" onclick="preencher30Dias()">+30 dias</button>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-pesquisar-custom">
                    <i class="fa fa-search"></i>
                    Pesquisar
                </button>

            </div>
        </div>
    </form>
</div>

<div class="clearfix" style="margin-bottom: 15px;">
    <a href="{{ route('add') }}" class="btn btn-primary pull-left">Cadastrar OS</a>
    @php($usuario = session('logado') ? (session('logado')) : null)
    @if($usuario && $usuario['admin'] == 1)
    <a href="{{ route('exportar', request()->all()) }}" class="btn btn-success pull-right">Exportar para Excel</a>
    @endif
</div>
@if(count($os) > 0)
<div class="alert alert-info" style="margin-bottom: 15px;">
    <strong><i class="fa fa-calculator"></i> Total das OS filtradas:</strong> 
    R$ {{ number_format($totalValor, 2, ',', '.') }}
    <small>({{ $os->total() }} registros encontrados)</small>
</div>

<table id="general-table" class="table table-striped table-bordered">
    <thead>
        <tr>
            <th>
                <a href="{{ route('index', array_merge(request()->query(), ['sortBy' => 'id', 'sortDir' => request('sortDir') == 'asc' ? 'desc' : 'asc'])) }}">
                    Código da O.S.
                    @if(request('sortBy') == 'id')
                    <i class="fa fa-sort-{{ request('sortDir') == 'asc' ? 'up' : 'down' }}"></i>
                    @endif
                </a>
            </th>
            <th width="40%">
                <div class="sort-links">
                    <a href="{{ route('index', array_merge(request()->query(), ['sortBy' => 'nome', 'sortDir' => request('sortDir') == 'asc' ? 'desc' : 'asc'])) }}">
                        Nome
                        @if(request('sortBy') == 'nome')
                        <i class="fa fa-sort-{{ request('sortDir') == 'asc' ? 'up' : 'down' }}"></i>
                        @endif
                    </a>
                    <span class="sort-separator">/</span>
                    <a href="{{ route('index', array_merge(request()->query(), ['sortBy' => 'valor_total', 'sortDir' => request('sortDir') == 'asc' ? 'desc' : 'asc'])) }}">
                        Valor
                        @if(request('sortBy') == 'valor_total')
                        <i class="fa fa-sort-{{ request('sortDir') == 'asc' ? 'up' : 'down' }}"></i>
                        @endif
                    </a>
                </div>
            </th>
            <th>
                <a href="{{ route('index', array_merge(request()->query(), ['sortBy' => 'loja', 'sortDir' => request('sortDir') == 'asc' ? 'desc' : 'asc'])) }}">
                    Loja/Vendedor
                    @if(request('sortBy') == 'loja')
                    <i class="fa fa-sort-{{ request('sortDir') == 'asc' ? 'up' : 'down' }}"></i>
                    @endif
                </a>
            </th>
            <th>
                <a href="{{ route('index', array_merge(request()->query(), ['sortBy' => 'dt_emissao', 'sortDir' => request('sortDir') == 'asc' ? 'desc' : 'asc'])) }}">
                    Data de Emissão
                    @if(request('sortBy') == 'dt_emissao')
                    <i class="fa fa-sort-{{ request('sortDir') == 'asc' ? 'up' : 'down' }}"></i>
                    @endif
                </a>
            </th>
            <th style="width: 240px;" class="text-center">Opções</th>
        </tr>
    </thead>
    <tbody>
        @foreach( $os as $product )
        <tr>
            <td>{{ $product->id }}</td>
            <td>
                {{ strtoupper( $product->nome ) }}
                @if(!empty($product->fones))
                @php($primeiro_fone = trim(preg_split('/[\\,\/]/', $product->fones)[0]))
                @if(!empty($primeiro_fone))
                <br>
                <a href="https://api.whatsapp.com/send?phone=55{{ \App\Helpers\Helpers::sanitizePhone($primeiro_fone) }}" target="_blank">
                    <i class="fa fa-whatsapp"></i> {{ $product->fones }}
                </a>
                @endif
                @endif
                <br>
                <small>
                    @if(!empty($product->armacao))
                    <span class="d-block"><strong class="fw-bold">Armação:</strong> {{ strtolower($product->armacao) }}
                        @endif
                        @if(!empty($product->lentes))
                        <br />
                        <strong class="fw-bold">Lentes:</strong> {{ strtolower($product->lentes) }}
                        @endif
                        @if(!empty($product->valor_total))
                        <br />
                        <span class="d-block"><strong class="fw-bold">Total:</strong> R$ {{$product->valor_total}}</span>
                        @endif

                </small>
            </td>
            <td>
                @if($product->filial)
                <strong>{{ $product->filial->nome }}</strong>
                @else
                <em>Loja não informada</em>
                @endif
                @if(!empty($product->vendedor))
                <br>{{ $product->vendedor }}
                @endif
            </td>
            <td>
                {{ $product->dt_emissao }}
                <br>
                @if(!empty($product->dt_prev_entrega))
                <small><strong class="fw-bold">Prev: </strong>{{ $product->dt_prev_entrega }}</small>
                @endif
            </td>
            <td>
                <div class="btn-group" role="group" aria-label="Opções da O.S.">
                    <a href="{{ route('edit', $product->id) }}" title="Editar O.S." class="btn btn-info"><i class="fa fa-pencil-square-o"></i></a>
                    <a href="{{ route('remove', $product->id) }}" title="Remover O.S." class="btn btn-danger"><i class="fa fa-trash"></i></a>
                    <a href="{{ route('pdf', $product->id) }}" title="Imprimir" class="btn btn-success" target="_blank"><i class="fa fa-file-pdf-o"></i></a>
                    <a href="{{ route('pdf_cliente', $product->id) }}" title="Imprimir - Somente a via do cliente" class="btn btn-success" target="_blank"><i class="fa fa-file-o"></i></a>
                </div>
                <br />
                <div class="btn-group mt-2" role="group" aria-label="Opções da O.S.">
                    <a href="{{ route('duplicar', $product->id) }}" title="Duplicar O.S." class="btn btn-default" target="_blank"><i class="fa fa-clone" aria-hidden="true"></i></a>
                    @if(\App\Helpers\Helpers::sanitizePhone($primeiro_fone) != '')
                    <button class="btn btn-default" onclick="copiarTelefone('{{ \App\Helpers\Helpers::sanitizePhone($primeiro_fone) }}')" title="Copiar Telefone"><i class="fa fa-phone"></i></button>
                    @endif
                </div>
            </td>
        </tr>
        @endforeach
    </tbody>
</table>

{{ $os->appends(Request::input())->links() }}
@else
<p class="well"> Nenhum registro </p>
@endif

@section('scripts')
<script>
    // Funções para os botões de atalho
    function formatarData(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    function preencherHoje() {
        const hoje = new Date();
        const dataFormatada = formatarData(hoje);

        document.querySelector('input[name="dt_prev_entrega_inicio"]').value = dataFormatada;
        document.querySelector('input[name="dt_prev_entrega_fim"]').value = dataFormatada;

        // Limpa os campos de período de emissão
        limparPeriodoEmissao();
    }

    function preencherSemana() {
        const hoje = new Date();
        const semanaDepois = new Date();
        semanaDepois.setDate(hoje.getDate() + 7);

        document.querySelector('input[name="dt_prev_entrega_inicio"]').value = formatarData(hoje);
        document.querySelector('input[name="dt_prev_entrega_fim"]').value = formatarData(semanaDepois);

        // Limpa os campos de período de emissão
        limparPeriodoEmissao();
    }

    function preencher30Dias() {
        const hoje = new Date();
        const trintaDiasAtras = new Date();
        trintaDiasAtras.setDate(hoje.getDate() - 30);

        document.querySelector('input[name="dt_prev_entrega_inicio"]').value = formatarData(trintaDiasAtras);
        document.querySelector('input[name="dt_prev_entrega_fim"]').value = formatarData(trintaDiasAtras);

        // Limpa os campos de período de emissão
        limparPeriodoEmissao();
    }

    function limparPeriodoEmissao() {
        const dataInicioField = document.querySelector('input[name="data_inicio"]');
        const dataFimField = document.querySelector('input[name="data_fim"]');

        if (dataInicioField) dataInicioField.value = '';
        if (dataFimField) dataFimField.value = '';
    }

    function limparPeriodoPrevEntrega() {
        const dtPrevEntregaInicioField = document.querySelector('input[name="dt_prev_entrega_inicio"]');
        const dtPrevEntregaFimField = document.querySelector('input[name="dt_prev_entrega_fim"]');

        if (dtPrevEntregaInicioField) dtPrevEntregaInicioField.value = '';
        if (dtPrevEntregaFimField) dtPrevEntregaFimField.value = '';
    }

    function preencher11Meses() {
        const hoje = new Date();
        const trezeMesesAtras = new Date();
        trezeMesesAtras.setMonth(hoje.getMonth() - 13);

        document.querySelector('input[name="data_inicio"]').value = formatarData(trezeMesesAtras);
        document.querySelector('input[name="data_fim"]').value = formatarData(trezeMesesAtras);

        // Limpa os campos de período de Dt. Prev. Entrega
        limparPeriodoPrevEntrega();
    }

    function preencher1Ano() {
        const hoje = new Date();
        const quatorzeMesesAtras = new Date();
        quatorzeMesesAtras.setMonth(hoje.getMonth() - 12);

        document.querySelector('input[name="data_inicio"]').value = formatarData(quatorzeMesesAtras);
        document.querySelector('input[name="data_fim"]').value = formatarData(quatorzeMesesAtras);

        // Limpa os campos de período de Dt. Prev. Entrega
        limparPeriodoPrevEntrega();
    }


    function copiarTelefone(telefone) {
        navigator.clipboard.writeText(telefone).then(function() {
            alert('Telefone ' + telefone + ' copiado!');
        }).catch(function(error) {
            console.error('Erro ao copiar telefone: ', error);
            // Fallback para navegadores mais antigos
            try {
                const tempInput = document.createElement('input');
                tempInput.value = telefone;
                document.body.appendChild(tempInput);
                tempInput.select();
                document.execCommand('copy');
                document.body.removeChild(tempInput);
                alert('Telefone copiado: ' + telefone);
            } catch (err) {
                alert('Não foi possível copiar o telefone.');
            }
        });
    }

    // Função para limpar campos de período quando Dt. Prev. Entrega for preenchido
    document.addEventListener('DOMContentLoaded', function() {
        const dtPrevEntregaInicioField = document.querySelector('input[name="dt_prev_entrega_inicio"]');
        const dtPrevEntregaFimField = document.querySelector('input[name="dt_prev_entrega_fim"]');

        if (dtPrevEntregaInicioField) {
            dtPrevEntregaInicioField.addEventListener('change', function() {
                if (this.value) {
                    limparPeriodoEmissao();
                }
            });
        }

        if (dtPrevEntregaFimField) {
            dtPrevEntregaFimField.addEventListener('change', function() {
                if (this.value) {
                    limparPeriodoEmissao();
                }
            });
        }
    });
</script>
@stop

@stop
