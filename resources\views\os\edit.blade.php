@extends('template.master')

@section('sidebar')

@stop

@section('content')
<?php
$longe = json_decode($oticas->longe);
$perto = json_decode($oticas->perto);
?>


{{ Form::model( $oticas,
    array(
        'enctype'                           =>  'multipart/form-data',
        'method'                            =>  'post',
        'class'                             =>  'form-horizontal form-bordered',
    ))
}}

@if ( $errors->count() > 0 )
<div class="alert alert-danger alert-dismissable">
    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
    <h4><i class="fa fa-times-circle"></i>
        <strong>
            Erros de preenchimento
        </strong>
    </h4>
    @foreach( $errors->all() as $message )
    {{ $message }}
    <br />
    @endforeach
</div>
@endif



<div class="form-group col-sm-6">
    <label for="id_filial" class="col-sm-4 control-label">Filiais:</label>
    <div class="col-sm-8">
        {{ Form::select('id_filial', $filiais, old('id_filial'), ['class'=>'form-control']) }}
    </div>
</div>

<div class="form-group  col-sm-6">
    <label for="os" class="col-sm-10 control-label">O.S.</label>
    <div class="col-sm-2">
        {{ Form::text( 'cod_os', old('cod_os'), array( 'class' => 'form-control', 'id'=>'id', 'placeholder'=>'O.S.', 'autocomplete'=>'off', 'disabled'=>'disabled', 'class'=>'disabled form-control' )) }}
    </div>
</div>
<div class="form-group col-sm-6">
    <label for="dt_emissao" class="col-sm-4 control-label">Data de Emissão:</label>
    <div class="col-sm-8">
        <input type="date"
            class="form-control"
            id="dt_emissao"
            name="dt_emissao"
            value="{{ old('dt_emissao', $oticas->dt_emissao ? \Carbon\Carbon::createFromFormat('d/m/Y', $oticas->dt_emissao)->format('Y-m-d') : '') }}"
            autocomplete="off">
    </div>
</div>
<div class="form-group col-sm-6">
    <label for="dt_prev_entrega" class="col-sm-4 control-label">Previsão de Entrega</label>
    <div class="col-sm-8">
        <div class="input-group">
            <input type="date"
                class="form-control"
                id="dt_prev_entrega"
                name="dt_prev_entrega"
                value="{{ old('dt_prev_entrega', $oticas->dt_prev_entrega ? \Carbon\Carbon::createFromFormat('d/m/Y', $oticas->dt_prev_entrega)->format('Y-m-d') : '') }}"
                autocomplete="off">
            <div class="input-group-btn">
                <button type="button" class="btn btn-default" onclick="calcularPrevisaoEntrega(3)">3</button>
                <button type="button" class="btn btn-default" onclick="calcularPrevisaoEntrega(7)">7</button>
                <button type="button" class="btn btn-default" onclick="calcularPrevisaoEntrega(10)">10</button>
            </div>
        </div>
    </div>
</div>
<div class="form-group col-sm-12">
    <label for="nome" class="col-sm-2 control-label">Nome:</label>
    <div class="col-sm-10">
        {{ Form::text('nome', old('nome'), array( 'class' => 'form-control', 'id'=>'nome', 'autocomplete'=>'off', 'class'=>'form-control' )) }}
    </div>
</div>
<div class="form-group">
    <label for="endereco" class="col-sm-2 control-label">Endereço:</label>
    <div class="col-sm-10">
        {{ Form::text('endereco', old('endereco'), array( 'class' => 'form-control', 'id'=>'endereco', 'autocomplete'=>'off', 'class'=>'form-control' )) }}
    </div>
</div>
<div class="form-group">
    <label for="fones" class="col-sm-2 control-label">Fones:</label>
    <div class="col-sm-10">
        {{ Form::text('fones', old('fones'), array( 'class' => 'form-control', 'id'=>'fones', 'autocomplete'=>'off', 'class'=>'form-control' )) }}
    </div>
</div>

<!--VALORES-->

<div class="form-group col-sm-4">
    <label for="vl_total" class="col-sm-4 control-label text-right">Valor Total:</label>
    <div class="col-sm-8">
        <div class="input-group">
            <span class="input-group-addon">R$</span>
            {{ Form::text('valor_total', old('valor_total'), array( 'class' => 'form-control dinheiro', 'id'=>'valor_total', 'autocomplete'=>'off' )) }}
        </div>
    </div>
</div>
<div class="form-group col-sm-4">
    <label for="pago" class="col-sm-4 control-label text-right">Pago:</label>
    <div class="col-sm-8">
        <div class="input-group">
            <span class="input-group-addon">R$</span>
            {{ Form::text('pago', old('pago'), array( 'class' => 'form-control dinheiro', 'id'=>'pago', 'autocomplete'=>'off' )) }}
        </div>
    </div>
</div>
<div class="form-group col-sm-4">
    <label for="a_pagar" class="col-sm-4 control-label text-right">À pagar:</label>
    <div class="col-sm-8">
        <div class="input-group">
            <span class="input-group-addon">R$</span>
            {{ Form::text('a_pagar', old('a_pagar'), array( 'class' => 'form-control dinheiro', 'id'=>'a_pagar', 'autocomplete'=>'off' )) }}
        </div>
    </div>
</div>

<!-- ASSINATURA -->

<div class="form-group col-sm-6">
    <label for="vendedor" class="col-sm-4 control-label">Vendedor(a):</label>
    <div class="col-sm-8">
        {{ Form::text('vendedor', old('vendedor'), array( 'class' => 'form-control', 'id'=>'vendedor', 'autocomplete'=>'off', 'class'=>'form-control' )) }}
    </div>
</div>



<div class="form-group col-sm-12">
    <label for="perto" class="col-sm-2 control-label">LONGE</label>
    <div class="col-sm-10">
        <table class="table table-bordered col-sm-10">
            <tr>
                <th></th>
                <th>ESF</th>
                <th>CIL</th>
                <th>EIXO</th>
                <th>DNP</th>
                <th>ALTURA</th>
            </tr>
            <tr>
                <td>O.D.</td>
                <td>
                    <select name="longe[od][esf_sig]">
                        <option value="+" <?php if (substr($longe->od->esf, 0, 1) == "+") {
                                                echo "selected";
                                            } ?>>+</option>
                        <option value="-" <?php if (substr($longe->od->esf, 0, 1) == "-") {
                                                echo "selected";
                                            } ?>>-</option>
                    </select>
                    {{ Form::text('longe[od][esf]', old('longe[od][esf]', strlen($longe->od->esf) > 0 ? substr($longe->od->esf, 1) : ""), array('class'=>'', 'id'=>'longe[od][esf]', 'autocomplete'=>'off')) }}
                </td>
                <td>
                    - {{ Form::text('longe[od][cil]', old('longe[od][cil]', str_replace("-", "", $longe->od->cil)), array('class'=>'cil', 'id'=>'longe[od][cil]', 'autocomplete'=>'off')) }}
                </td>
                <td>
                    {{ Form::text('longe[od][eixo]', old('longe[od][eixo]', $longe->od->eixo), array('class'=>'', 'id'=>'longe[od][eixo]', 'autocomplete'=>'off')) }}
                    º
                </td>
                <td>
                    {{ Form::text('longe[od][dnp]', old('longe[od][dnp]', $longe->od->dnp), array('class'=>'dinheiro', 'id'=>'longe[od][dnp]', 'autocomplete'=>'off')) }}
                </td>
                <td>
                    {{ Form::text('longe[od][altura]', old('longe[od][altura]', $longe->od->altura), array('class'=>'dinheiro', 'id'=>'longe[od][altura]', 'autocomplete'=>'off')) }}
                </td>
            </tr>
            <tr>
                <td>O.E.</td>
                <td>
                    <select name="longe[oe][esf_sig]">
                        <option value="+" <?php if (substr($longe->oe->esf, 0, 1) == "+") {
                                                echo "selected";
                                            } ?>>+</option>
                        <option value="-" <?php if (substr($longe->oe->esf, 0, 1) == "-") {
                                                echo "selected";
                                            } ?>>-</option>
                    </select>
                    {{ Form::text('longe[oe][esf]', old('longe[oe][esf]', strlen($longe->oe->esf) > 0 ? substr($longe->oe->esf, 1) : ""), array('class'=>'', 'id'=>'longe[oe][esf]', 'autocomplete'=>'off')) }}
                </td>
                <td>
                    - {{ Form::text('longe[oe][cil]', old('longe[oe][cil]', str_replace("-", "", $longe->oe->cil)), array('class'=>'cil', 'id'=>'longe[oe][cil]', 'autocomplete'=>'off')) }}
                </td>
                <td>
                    {{ Form::text('longe[oe][eixo]', old('longe[oe][eixo]', $longe->oe->eixo), array('class'=>'', 'id'=>'longe[oe][eixo]', 'autocomplete'=>'off')) }}
                    º
                </td>
                <td>
                    {{ Form::text('longe[oe][dnp]', old('longe[oe][dnp]', $longe->oe->dnp), array('class'=>'dinheiro', 'id'=>'longe[oe][dnp]', 'autocomplete'=>'off')) }}
                </td>
                <td>
                    {{ Form::text('longe[oe][altura]', old('longe[oe][altura]', $longe->oe->altura), array('class'=>'dinheiro', 'id'=>'longe[oe][altura]', 'autocomplete'=>'off')) }}
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="form-group col-sm-12">
    <label for="perto" class="col-sm-2 control-label">PERTO</label>
    <div class="col-sm-10">
        <table class="table table-bordered col-sm-10">
            <tr>
                <th></th>
                <th>ESF</th>
                <th>CIL</th>
                <th>EIXO</th>
                <th>DNP</th>
                <th>ALTURA</th>
            </tr>
            <tr>
                <td>O.D.</td>
                <td>
                    <select name="perto[od][esf_sig]">
                        <option value="+" <?php if (substr($perto->od->esf, 0, 1) == "+") {
                                                echo "selected";
                                            } ?>>+</option>
                        <option value="-" <?php if (substr($perto->od->esf, 0, 1) == "-") {
                                                echo "selected";
                                            } ?>>-</option>
                    </select>
                    {{ Form::text('perto[od][esf]', old('perto[od][esf]', strlen($perto->od->esf) > 0 ? substr($perto->od->esf, 1) : ""), array('class'=>'', 'id'=>'perto[od][esf]', 'autocomplete'=>'off')) }}
                </td>
                <td>
                    - {{ Form::text('perto[od][cil]', old('perto[od][cil]', str_replace("-", "", $perto->od->cil)), array('class'=>'cil', 'id'=>'perto[od][cil]', 'autocomplete'=>'off')) }}
                </td>
                <td>
                    {{ Form::text('perto[od][eixo]', old('perto[od][eixo]', $perto->od->eixo), array('class'=>'', 'id'=>'perto[od][eixo]', 'autocomplete'=>'off')) }}
                    º
                </td>
                <td>
                    {{ Form::text('perto[od][dnp]', old('perto[od][dnp]', $perto->od->dnp), array('class'=>'dinheiro', 'id'=>'perto[od][dnp]', 'autocomplete'=>'off')) }}
                </td>
                <td>
                    {{ Form::text('perto[od][altura]', old('perto[od][altura]', $perto->od->altura), array('class'=>'dinheiro', 'id'=>'perto[od][altura]', 'autocomplete'=>'off')) }}
                </td>
            </tr>
            <tr>
                <td>O.E.</td>
                <td>
                    <select name="perto[oe][esf_sig]">
                        <option value="+" <?php if (substr($perto->oe->esf, 0, 1) == "+") {
                                                echo "selected";
                                            } ?>>+</option>
                        <option value="-" <?php if (substr($perto->oe->esf, 0, 1) == "-") {
                                                echo "selected";
                                            } ?>>-</option>
                    </select>
                    {{ Form::text('perto[oe][esf]', old('perto[oe][esf]', strlen($perto->oe->esf) > 0 ? substr($perto->oe->esf, 1) : ""), array('class'=>'', 'id'=>'perto[oe][esf]', 'autocomplete'=>'off')) }}
                </td>
                <td>
                    - {{ Form::text('perto[oe][cil]', old('perto[oe][cil]', str_replace("-", "", $perto->oe->cil)), array('class'=>'cil', 'id'=>'perto[oe][cil]', 'autocomplete'=>'off')) }}
                </td>
                <td>
                    {{ Form::text('perto[oe][eixo]', old('perto[oe][eixo]', $perto->oe->eixo), array('class'=>'', 'id'=>'perto[oe][eixo]', 'autocomplete'=>'off')) }}
                    º
                </td>
                <td>
                    {{ Form::text('perto[oe][dnp]', old('perto[oe][dnp]', $perto->oe->dnp), array('class'=>'dinheiro', 'id'=>'perto[oe][dnp]', 'autocomplete'=>'off')) }}
                </td>
                <td>
                    {{ Form::text('perto[oe][altura]', old('perto[oe][altura]', $perto->oe->altura), array('class'=>'dinheiro', 'id'=>'perto[oe][altura]', 'autocomplete'=>'off')) }}
                </td>
            </tr>
        </table>
    </div>
</div>
<div class="form-group">
    <label for="adicao" class="col-sm-2 control-label">Adição:</label>
    <div class="col-sm-10">
        {{ Form::text('adicao', old('adicao'), array( 'class' => 'form-control', 'id'=>'adicao', 'maxlength'=>10, 'autocomplete'=>'off', 'class'=>'form-control' )) }}
    </div>
</div>
<div class="form-group">
    <label for="armacao_tipo" class="col-sm-2 control-label">Armação:</label>
    <div class="col-sm-10">
        <div class="col-sm-3">
            <div class="radio">
                <label>
                    {{ Form::radio('armacao_propria', '0', old('armacao_propria', $oticas->armacao_propria) == '0' || old('armacao_propria', $oticas->armacao_propria) === false, ['id' => 'armacao_loja']) }} Loja
                </label>
            </div>
        </div>
        <div class="col-sm-3">
            <div class="radio">
                <label>
                    {{ Form::radio('armacao_propria', '1', old('armacao_propria', $oticas->armacao_propria) == '1' || old('armacao_propria', $oticas->armacao_propria) === true, ['id' => 'armacao_propria']) }} Própria
                </label>
            </div>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="armacao" class="col-sm-2 control-label">Descrição da Armação:</label>
    <div class="col-sm-10">
        {{ Form::text('armacao', old('armacao'), array( 'placeholder'=>'Ref. e nome', 'class' => 'form-control', 'id'=>'armacao', 'maxlength'=>200, 'autocomplete'=>'off', 'class'=>'form-control' )) }}
    </div>
</div>
<div class="form-group">
    <label for="armacao_sem_garantia" class="col-sm-2 control-label">Alerta da armação:</label>
    <div class="col-sm-10">
        <div class="checkbox">
            <label>
                {{ Form::checkbox('armacao_sem_garantia', '1', old('armacao_sem_garantia', $oticas->armacao_sem_garantia), ['id' => 'armacao_sem_garantia']) }} Escolhi lentes sem garantia de quebra/trincados
            </label>
        </div>
    </div>
</div>
<div class="form-group">
    <label for="lentes" class="col-sm-2 control-label">Lentes:</label>
    <div class="col-sm-10">
        {{ Form::text('lentes', old('lentes'), array( 'class' => 'form-control', 'id'=>'lentes', 'maxlength'=>200, 'autocomplete'=>'off', 'class'=>'form-control' )) }}
    </div>
</div>
<div class="form-group">
    <label for="obs" class="col-sm-2 control-label">Obs.:</label>
    <div class="col-sm-10">
        {{ Form::textarea('obs', old('obs'), array( 'class' => 'form-control', 'id'=>'obs', 'autocomplete'=>'off', 'class'=>'form-control' )) }}
    </div>
</div>
<div class="form-group">
    <label for="medico" class="col-sm-2 control-label">Médico:</label>
    <div class="col-sm-10">
        {{ Form::text('medico', old('medico'), array( 'class' => 'form-control', 'id'=>'medico', 'autocomplete'=>'off', 'class'=>'form-control' )) }}
    </div>
</div>


<div class="form-group">
    <label for="medico" class="col-sm-2 control-label">Laboratório:</label>
    <div class="col-sm-10">
        {{ Form::text('laboratorio', old('laboratorio'), array( 'class' => 'form-control', 'id'=>'laboratorio', 'autocomplete'=>'off', 'class'=>'form-control' )) }}
    </div>
</div>
<div class="form-group">
    <label for="formas" class="col-sm-2 control-label">Formas de pagamento:</label>
    <div class="col-sm-10">
        <div class="col-sm-3">
            <div class="checkbox">
                <label>
                    {{ Form::radio('forma_de_pagamento', 'w') }} Dinheiro
                </label>
            </div>
            <div class="checkbox">
                <label>
                    {{ Form::radio('forma_de_pagamento', 'p') }} Pix
                </label>
            </div>
        </div>
        <div class="col-sm-3">
            <div class="checkbox">
                <label>
                    {{ Form::radio('forma_de_pagamento', 'c') }} Crédito
                </label>
            </div>
            <div class="checkbox">
                <label>
                    {{ Form::radio('forma_de_pagamento', 'd') }} Debito
                </label>
            </div>

            <div class="checkbox">
                <label>
                    {{ Form::radio('forma_de_pagamento', 'l') }} Link de pagamento
                </label>
            </div>
        </div>
        <div class="col-sm-3">
            <div class="checkbox">
                <label>
                    {{ Form::radio('forma_de_pagamento', 'o') }} Outros {{ Form::text('forma_de_pagamento_outros', old('forma_de_pagamento_outros'), array( 'class' => 'form-control', 'id'=>'nome', 'autocomplete'=>'off', 'class'=>'form-control' )) }}
                </label>

            </div>
        </div>
    </div>
</div>

<div>
    <fieldset>
        <legend>Confirmação Laboratório</legend>
        <br class="clearfix" style="clear: both" />
        <div class="form-group col-sm-6">
            <label for="confirmado_lab" class="col-sm-3 control-label">Confirmação:</label>
            <div class="col-sm-9">
                <input type="date"
                    class="form-control"
                    id="confirmado_lab"
                    name="confirmado_lab"
                    value="{{ old('confirmado_lab', $oticas->confirmado_lab ? date('Y-d-m', strtotime($oticas->confirmado_lab)) : '') }}"
                    autocomplete="off">
            </div>
        </div>
    </fieldset>
</div>

<div>
    <fieldset>
        <legend>Baixa da O.S.</legend>
        <br class="clearfix" style="clear: both" />
        <div class="form-group col-sm-6">
            <label for="baixa_data" class="col-sm-3 control-label">Data:</label>
            <div class="col-sm-9">
                <input type="date"
                    class="form-control"
                    id="baixa_data"
                    name="baixa_data"
                    value="{{ old('baixa_data', $oticas->baixa_data ? date('Y-d-m', strtotime($oticas->baixa_data)) : '') }}"
                    readonly="readonly"
                    autocomplete="off">
            </div>
        </div>

        <div class="form-group col-sm-6">
            <label for="valor_total" class="col-sm-3 control-label">Valor Total:</label>
            <div class="col-sm-9">
                {{ Form::text('baixa_valor', old('baixa_valor'), array( 'class' => 'form-control', 'id'=>'baixa_valor', 'autocomplete'=>'off', 'class'=>'dinheiro form-control', "readonly"=>"readonly" )) }}
            </div>
        </div>
        <div class="form-group col-sm-12">
            <label for="obs" class="col-sm-2 control-label">Obs.:</label>
            <div class="col-sm-8">
                {{ Form::textarea('baixa_observacao', old('baixa_observacao'), array( 'class' => 'form-control', 'id'=>'baixa_observacao', 'autocomplete'=>'off', 'class'=>'form-control', "readonly"=>"readonly" )) }}
            </div>
        </div>
    </fieldset>
</div>

<div class="form-group">
    <div class="col-sm-offset-2 col-sm-10">
        <button type="submit" class="btn btn-primary">Salvar</button>
        <a href="{{ route('index') }}" class="btn btn-default">Voltar</a>
    </div>
</div>


{{ Form::close() }}
@stop