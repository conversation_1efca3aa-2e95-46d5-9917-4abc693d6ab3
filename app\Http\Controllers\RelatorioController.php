<?php

namespace App\Http\Controllers;


use App\Http\Controllers\Controller;
use App\Models\Despesa;
use App\Models\OticaOS;
use App\Models\Usuario;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class RelatorioController extends Controller
{

    /*
	|--------------------------------------------------------------------------
	| Default Home Controller
	|--------------------------------------------------------------------------
	|
	| You may wish to use controllers instead of, or in addition to, Closure
	| based routes. That's great! Here is an example controller method to
	| get you started. To route to this controller, just add the route:
	|
	|	Route::get('/', 'HomeController@showWelcome');
	|
	*/
    public function RelatorioFormas(Request $request)
    {
        $usuario = ($request->session()->get("logado"));

        if (isset($_GET["data_inicio"])) {
            $dataI = $_GET["data_inicio"];
            $dataF = $_GET["data_fim"];

            $results = DB::select(DB::raw("SELECT SUM(valor_total) as Valor, forma_de_pagamento as Forma, MONTH(dt_emissao) as Mes, YEAR(dt_emissao) as  Ano
FROM oticasos
WHERE dt_emissao >= '" . $dataI . "' AND dt_emissao <= '" . $dataF . "'
AND id_empresa = " . $usuario["id_empresa"] . "
GROUP BY YEAR(dt_emissao), MONTH(dt_emissao), forma_de_pagamento ORDER BY YEAR(dt_emissao) DESC, MONTH(dt_emissao) DESC, forma_de_pagamento;"));
        } else {
            $results = DB::select(DB::raw("SELECT SUM(valor_total) as Valor, forma_de_pagamento as Forma, MONTH(dt_emissao) as Mes, YEAR(dt_emissao) as  Ano
FROM oticasos
WHERE dt_emissao >= curdate() - interval (dayofmonth(curdate()) - 1) day - interval 6 month
AND id_empresa = " . $usuario["id_empresa"] . "
GROUP BY YEAR(dt_emissao), MONTH(dt_emissao), forma_de_pagamento ORDER BY YEAR(dt_emissao) DESC, MONTH(dt_emissao) DESC, forma_de_pagamento;"));
        }


        $dados["rel_dados_formas"] = $results;

        return response()->view("rel.rel_formas", ["dados" => $dados]);
    }

    public function RelatorioReceitaDespesa(Request $request)
    {
        $usuario = ($request->session()->get("logado"));
        if (isset($_GET["data_inicio"])) {
            $dataI = $_GET["data_inicio"];
            $dataF = $_GET["data_fim"];
        } else {
            $dataI = date("Y-m-d");
            $dataF = date("Y-m-d");
        }

        $despesa = DB::select(DB::raw("SELECT SUM(valor) as Valor FROM despesas
  WHERE
    dt_vencimento >= '" . $dataI . "' AND dt_vencimento <= '" . $dataF . "' AND id_empresa = " . $usuario["id_empresa"] . "  "));

        $receita_pagas = DB::select(DB::raw("SELECT SUM(pago) as Valor FROM oticasos
  WHERE
  dt_emissao >= '" . $dataI . "' AND dt_emissao <= '" . $dataF . "'  AND id_empresa = " . $usuario["id_empresa"] . "   "));

        $receita_baixas = DB::select(DB::raw("SELECT SUM(baixa_valor) as Valor FROM oticasos
  WHERE
  baixa_data >= '" . $dataI . "' AND baixa_data <= '" . $dataF . "' AND baixa_status = 'F'  AND id_empresa = " . $usuario["id_empresa"] . "   "));

        //print_r($receita_pagas[0]->Valor + $receita_baixas[0]->Valor);
        $dados["receita"] = $receita_pagas[0]->Valor + $receita_baixas[0]->Valor;
        //$dados["receita"] = $receita;
        $dados["despesa"] = $despesa;
        $dados["dataI"] = $dataI;
        $dados["dataF"] = $dataF;

        return response()->view("rel.rel_receitadespesa", ["dados" => $dados]);
    }


    public function RelatorioGeral(Request $request)
    {
        $usuario = ($request->session()->get("logado"));

        $dados["atrasados"] = Despesa::where("situacao", "A")->where('id_empresa', $usuario["id_empresa"])->count();
        $dados["atrasados_total"] = Despesa::where("situacao", "A")->where('id_empresa', $usuario["id_empresa"])->sum("valor");

        $dados["despesas"] = Despesa::where("situacao", "E")->where("dt_vencimento", 'LIKE', '%' . date("Y-m") . '%')->where('id_empresa', $usuario["id_empresa"])->count();
        $dados["despesas_total"] = Despesa::where("situacao", "E")->where("dt_vencimento", 'LIKE', '%' . date("Y-m") . '%')->where('id_empresa', $usuario["id_empresa"])->sum("valor");

        $dados["receitas_atrasadas"] = OticaOS::where("a_pagar", '>', 0)->where("baixa_status", "!=", "F")->where('id_empresa', $usuario["id_empresa"])->count();
        $dados["receitas_atrasadas_total"] = OticaOS::where("a_pagar", ">", 0)->where("baixa_status", "!=", "F")->where('id_empresa', $usuario["id_empresa"])->sum("a_pagar");

        $dados["receitas"] = OticaOS::where("dt_emissao", 'LIKE', '%' . date("Y-m") . '%')->where('id_empresa', $usuario["id_empresa"])->count();
        $dados["receitas_total"] = OticaOS::where("dt_emissao", 'LIKE', '%' . date("Y-m") . '%')->where('id_empresa', $usuario["id_empresa"])->sum("pago");

        $dados["receitas_total_apagar"] = OticaOS::where("dt_emissao", 'LIKE', '%' . date("Y-m") . '%')->where('id_empresa', $usuario["id_empresa"])->sum("a_pagar");
        $dados["receitas_total_baixado_mes"] = OticaOS::where("baixa_data", 'LIKE', date("Y-m") . '%')->where('id_empresa', $usuario["id_empresa"])->sum("baixa_valor");

        $dados["receitas_dia"] = OticaOS::where("dt_emissao", 'LIKE', '%' . date("Y-m-d") . '%')->where('id_empresa', $usuario["id_empresa"])->count();
        $dados["receitas_total_dia"] = OticaOS::where("dt_emissao", 'LIKE', '%' . date("Y-m-d") . '%')->where('id_empresa', $usuario["id_empresa"])->sum("pago");

        $dados["receitas_total_apagar_dia"] = OticaOS::where("dt_emissao", 'LIKE', '%' . date("Y-m-d") . '%')->where('id_empresa', $usuario["id_empresa"])->sum("a_pagar");



        $results = OticaOS::where("dt_emissao", 'LIKE', '%' . date("Y") . '%')->where('id_empresa', $usuario["id_empresa"])->sum("pago") + OticaOS::where("baixa_data", 'LIKE', '%' . date("Y") . '%')->where("baixa_status", "F")->where('id_empresa', $usuario["id_empresa"])->sum("baixa_valor");
        $dados["valor_total_receita_ano"] = $results;

        return response()->view("rel.rel_geral", ["dados" => $dados]);
    }
}
