<html>
<?php
$longe = json_decode($oticas->longe);
$perto = json_decode($oticas->perto);
?>

<head>
  <style type="text/css">
    body {
      width: 17cm !important;
      max-width: 17cm !important;
      font-family: Arial, sans-serif;
      font-weight: normal;
    }

    .MsoTableGrid tr td {
      padding: 5px;
    }
  </style>
</head>

<body lang="PT-BR">



  <table width="100%">
    <tr>
      <td width="90pt">
        <img src="{{$filial->id}}.jpg" style="width:70pt" class="log" />
      </td>
      <td width="60%">
        {{$filial->nome}}
        <br />
        {{$filial->telefone}} | {{$filial->endereco}}
      </td>
      <td style="font-size: 20pt">
        <b>O.S.</b> <span style="text-decoration:underline;"> {{$oticas->id}} </span>
      </td>
    </tr>
  </table>
  <hr />
  <table>
    <tr>
      <td colspan="3">
        <b>Nome:</b> {{ $oticas->nome }} - <b>Telefone:</b> {{ $oticas->fones }}
      </td>
    </tr>

    <tr>
      <td>
        <b>Total:</b> R$ {{ $oticas->valor_total }}
      </td>
      <td style="text-align: center">
        <b>Pago:</b> R$ {{ $oticas->pago }}
      </td>
      <td>
        <b>À pagar:</b> R$ {{ $oticas->a_pagar }}
      </td>
    </tr>
  </table>
  <hr />
  <table width="100%">
    <tr>
      <td valign="middle" width="100pt">
        Endereço:
      </td>
      <td style="border-bottom: 1pt solid;">
        {{ $oticas->endereco}}
      </td>
    </tr>
  </table>

  <table width="100%">
    <tr>
      <td width="100%">
        Data de Emissão:
      </td>
      <td style="border-bottom: 1pt solid; width: 100%">
        {{ $oticas->dt_emissao}}
      </td>
      <td width="100%">
        Previsão de Entrega:
      </td>
      <td style="border-bottom: 1pt solid; width: 100%">
        {{ $oticas->dt_prev_entrega}}
      </td>
    </tr>
  </table>
  <table width="100%">
    <tr>
      <td style="width: 100%">
        Valor Total:
      </td>
      <td style="border-bottom: 1pt solid; width: 100%">
        {{$oticas->valor_total}}
      </td>
      <td style="width: 100%">
        Pago: R$
      </td>
      <td style="border-bottom: 1pt solid; width: 100%">
        {{$oticas->pago}}
      </td>
      <td style="width: 100%">
        À pagar: R$
      </td>
      <td style="border-bottom: 1pt solid; width: 100%">
        {{$oticas->a_pagar}}
      </td>
    </tr>
  </table>

  <table width="100%">
    <tr>
      <td valign="middle" width="100px">
        Armação:
      </td>
      <td style="border-bottom: 1pt solid;">
        [ @if(!$oticas->armacao_sem_garantia) x @else @endif] Loja [@if($oticas->armacao_sem_garantia) x @else @endif] Próprio - {{$oticas->armacao}}
      </td>
    </tr>
    @if($oticas->armacao_sem_garantia)
    <tr>
      <td valign="middle">
      </td>
      <td>
        [ x ] Escolhi lentes sem garantia de quebra/trincados
      </td>
    </tr>
    @endif
    <tr>
      <td valign="middle" width="100px">
        Lentes:
      </td>
      <td style="border-bottom: 1pt solid;">
        {{$oticas->lentes}}
      </td>
    </tr>
  </table>
  <br />
  <table width="100%">
    <tr>
      <td>
        Ass:
      </td>
      <td style="border-bottom: 1pt solid; width: 100%">

      </td>
      <td>
        Vendedor(a):
      </td>
      <td style="border-bottom: 1pt solid; width: 100%">
        {{$oticas->vendedor}}
      </td>
    </tr>
  </table>
  <br />


  <p style="font-size: 9pt">
    Não nos responsabilizamos por armações usadas/terceiros.
    <br />
    Estou ciente que as mercadorias no prazo de 180 dias após a
    data da entrega, serão doados.
    <br />
    A garantia para adaptação e/ou erro médico é válida por 90 dias, contados a partir da data constante no certificado. Marcas com garantia: Hoya, Essilor (Varilux), Zeiss e Freestyle.

    @if($oticas->armacao_sem_garantia)
    <br />
    Escolhi lentes sem garantia de quebra/trincados
    @endif
  </p>
  <p align=center style='text-align:center;font-size: 10pt'>{{env("EMAIL_LOJA")}}</p>

</body>

</html>