@extends('template.master')
@section('sidebar')


@stop
@section('content')

<div>
    <form class="form-inline">
        <div class="form-group">
            <label for="search">Buscar por:</label>
            <input type="text" class="form-control" id="search" name="search" value="{{ Request::get('search')}}" placeholder="Despesa">
        </div>
        <button type="submit" class="btn btn-default">Pesquisar</button>
    </form>
</div>
<a href="{{ route('despesas.add') }}" class="btn btn-primary">Cadastrar Despesa</a>
<br />
<br />
@if(count($os) > 0)
    <table id="general-table" class="table table-striped table-bordered">
        <thead>
            <tr>
                <th>Data de vencimento</th>
                <th>Descrição</th>
                <th>Situação</th>
                <th>Valor</th>
                <th style="width: 200px;" class="text-center">Opções</th>
            </tr>
        </thead>
        <tbody>
            @foreach( $os as $product )
            <tr>
                <td>{{ $product->dt_vencimento }}</td>
                <td>{{ strtoupper( $product->descricao ) }}</td>
                <td>{{ strtoupper( App\Models\Despesa::getNomeSituacao($product->situacao) ) }}</td>
                <td>{{ $product->valor }}</td>
                <td>
                    <a href="{{ route('despesas.edit', $product->id) }}" title="Editar Despesa" class="btn btn-info"><i class="fa fa-pencil-square-o"></i></a>
                    <a href="{{ route('despesas.remove', $product->id) }}" title="Remover Despesa" class="btn btn-danger"><i class="fa fa-trash"></i></a>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>

    {{ $os->appends(Request::input())->links() }}
@else
    <p class="well"> Nenhum registro </p>
@endif
@stop
