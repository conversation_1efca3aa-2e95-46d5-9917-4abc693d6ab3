<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Filial;
use App\Models\OticaOS;
use Illuminate\Contracts\Session\Session;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\OSExport;

class OSController extends Controller
{

    /*
	|--------------------------------------------------------------------------
	| Default Home Controller
	|--------------------------------------------------------------------------
	|
	| You may wish to use controllers instead of, or in addition to, Closure
	| based routes. That's great! Here is an example controller method to
	| get you started. To route to this controller, just add the route:
	|
	|	Route::get('/', 'HomeController@showWelcome');
	|
	*/

    public function Index(Request $request)
    {
        $usuario = ($request->session()->get("logado"));

        // Carrega filiais para o filtro
        $filiais = Filial::pluck('nome', 'id');

        // Parâmetros de ordenação
        $sortBy = $request->input('sortBy', 'id');
        $sortDir = $request->input('sortDir', 'desc');
        $allowedSorts = ['id', 'nome', 'dt_emissao', 'loja', 'valor_total'];

        if (!in_array($sortBy, $allowedSorts)) {
            $sortBy = 'id';
        }

        // Inicia a query base
        $baseQuery = OticaOS::query()->select('oticasos.*', 'filiais.nome as filial_nome')
            ->leftJoin('filiais', 'oticasos.id_filial', '=', 'filiais.id');

        // Aplica todos os filtros na query base
        $this->applyFilters($baseQuery, $request, $usuario);

        // Calcula o total sem paginação
        $totalValor = (clone $baseQuery)->sum('valor_total');

        // Query para paginação
        $os = clone $baseQuery;
        
        // Lógica de ordenação
        $sortColumn = match ($sortBy) {
            'nome' => 'oticasos.nome',
            'dt_emissao' => 'dt_emissao',
            'loja' => 'filial_nome',
            'valor_total' => 'oticasos.valor_total',
            default => 'oticasos.id',
        };

        $os->orderBy($sortColumn, $sortDir);
        $os = $os->paginate(15);

        return response()->view('os.index', [
            'os' => $os,
            'filiais' => $filiais,
            'sortBy' => $sortBy,
            'sortDir' => $sortDir,
            'totalValor' => $totalValor
        ]);
    }
    public function Duplicar(Request $request, $id)
    {
        //$oticaos = new OticaOS();

        $oticaos = OticaOS::find($id);
        $oticaos = $oticaos->replicate();
        $oticaos->dt_emissao = date("Y-m-d");
        $oticaos->dt_prev_entrega = null;
        $oticaos->valor_total = null;
        $oticaos->valor_total = null;
        $oticaos->pago = null;
        $oticaos->a_pagar = null;
        $oticaos->baixa_valor = null;
        $oticaos->baixa_observacao = null;
        $oticaos->baixa_data = null;
        $oticaos->baixa_status = null;
        $oticaos->obs = null;
        $oticaos->save();

        return redirect()->route('edit', array($oticaos->id));
    }
    public function Add(Request $request)
    {
        $filiais = Filial::pluck('nome', 'id');
        return response()->view('os.add', ['filiais' => $filiais]);
    }
    public function AddAction(Request $request)
    {
        $rules = array(
            "dt_emissao" => "required",
            "dt_prev_entrega" => "required",
            "longe" => "required",
            "perto" => "required",
            "forma_de_pagamento" => "",
            "nome" => "required|min:5|max:100",
            "endereco" => "min:5|max:100",
            "fones" => "required|min:5|max:100",
            "valor_total" => "required"
        );

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->route('add')->withErrors($validator)->withInput();
        } else {
            $usuario = $request->session()->get("logado");
            $oticaos = new OticaOS();
            $oticaos->id_filial = $request->input('id_filial');
            $oticaos->id_empresa = $usuario["id_empresa"];
            $oticaos->dt_emissao = implode("-", array_reverse(explode("/", $request->input('dt_emissao'))));
            $oticaos->dt_prev_entrega = implode("-", array_reverse(explode("/", $request->input('dt_prev_entrega'))));

            $longe = $request->input('longe');
            $perto = $request->input('perto');

            $longe["oe"]["esf"] = $longe["oe"]["esf_sig"] . $longe["oe"]["esf"];
            $perto["oe"]["esf"] = $perto["oe"]["esf_sig"] . $perto["oe"]["esf"];
            $longe["od"]["esf"] = $longe["od"]["esf_sig"] . $longe["od"]["esf"];
            $perto["od"]["esf"] = $perto["od"]["esf_sig"] . $perto["od"]["esf"];

            unset($longe["oe"]["esf_sig"]);
            unset($perto["oe"]["esf_sig"]);
            unset($longe["od"]["esf_sig"]);
            unset($perto["od"]["esf_sig"]);

            $oticaos->id_filial = $request->input('id_filial');
            $oticaos->longe = json_encode($longe);
            $oticaos->perto = json_encode($perto);
            $oticaos->adicao = $request->input('adicao');
            $oticaos->armacao = $request->input('armacao');
            $oticaos->lentes = $request->input('lentes');
            $oticaos->obs = $request->input('obs');
            $oticaos->medico = $request->input('medico');
            $oticaos->forma_de_pagamento = $request->input('forma_de_pagamento');
            $oticaos->forma_de_pagamento_outros = $request->input('forma_de_pagamento_outros');
            $oticaos->nome = $request->input('nome');
            $oticaos->endereco = $request->input('endereco');
            $oticaos->fones = $request->input('fones');
            $oticaos->armacao_propria = $request->input('armacao_propria');
            $oticaos->armacao_sem_garantia = $request->input('armacao_sem_garantia');

            $oticaos->valor_total = str_replace(",", ".", str_replace(".", "", $request->input('valor_total')));
            $oticaos->pago = str_replace(",", ".", str_replace(".", "", $request->input('pago')));
            if (empty($request->input('pago'))) {
                $oticaos->pago = 0.00;
            }

            $oticaos->a_pagar = str_replace(",", ".", str_replace(".", "", $request->input('a_pagar')));
            $oticaos->vendedor = $request->input('vendedor');
            $oticaos->status = 'A';


            if ($oticaos->a_pagar > 0) {
                $oticaos->baixa_status = "A";
            } else {
                $oticaos->baixa_status = "F";
            }

            $oticaos->save();

            return redirect()->route('index');
        }
        /*

        return "X";
		return View::make('home.add');
        */
    }
    public function Edit(Request $request, $id)
    {
        $usuario = ($request->session()->get("logado"));
        $filiais = Filial::pluck('nome', 'id');

        $oticaos = OticaOS::find($id);
        if ($usuario["id_empresa"] == $oticaos->id_empresa) {
            return response()->view('os.edit', ['oticas' => $oticaos, 'filiais' => $filiais]);
        } else {
            return response()->route('403');
        }
    }
    public function EditAction(Request $request, $id)
    {
        $rules = array(
            "dt_emissao" => "required",
            "dt_prev_entrega" => "required",
            "longe" => "required",
            "perto" => "required",
            "forma_de_pagamento" => "",
            "nome" => "required|min:5|max:100",
            "endereco" => "min:5|max:100",
            "fones" => "required|min:5|max:100",
            "valor_total" => "required"
        );

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->route('edit', array('id' => $id))->withErrors($validator)->withInput();
        } else {
            $usuario = ($request->session()->get("logado"));

            $oticaos = OticaOS::find($id);
            $oticaos->id_filial = $request->input('id_filial');
            $oticaos->id_empresa = $usuario["id_empresa"];
            $oticaos->dt_emissao = implode("-", array_reverse(explode("/", $request->input('dt_emissao'))));
            $oticaos->dt_prev_entrega = implode("-", array_reverse(explode("/", $request->input('dt_prev_entrega'))));

            $longe = $request->input('longe');
            $perto = $request->input('perto');

            $longe["oe"]["esf"] = $longe["oe"]["esf_sig"] . $longe["oe"]["esf"];
            $perto["oe"]["esf"] = $perto["oe"]["esf_sig"] . $perto["oe"]["esf"];
            $longe["od"]["esf"] = $longe["od"]["esf_sig"] . $longe["od"]["esf"];
            $perto["od"]["esf"] = $perto["od"]["esf_sig"] . $perto["od"]["esf"];

            unset($longe["oe"]["esf_sig"]);
            unset($perto["oe"]["esf_sig"]);
            unset($longe["od"]["esf_sig"]);
            unset($perto["od"]["esf_sig"]);

            $oticaos->longe = json_encode($longe);
            $oticaos->perto = json_encode($perto);
            $oticaos->adicao = $request->input('adicao');
            $oticaos->armacao = $request->input('armacao');
            $oticaos->lentes = $request->input('lentes');
            $oticaos->obs = $request->input('obs');
            $oticaos->medico = $request->input('medico');
            $oticaos->forma_de_pagamento = $request->input('forma_de_pagamento');
            $oticaos->forma_de_pagamento_outros = $request->input('forma_de_pagamento_outros');
            $oticaos->nome = $request->input('nome');
            $oticaos->endereco = $request->input('endereco');
            $oticaos->fones = $request->input('fones');
            $oticaos->armacao_propria = $request->input('armacao_propria');
            $oticaos->armacao_sem_garantia = $request->input('armacao_sem_garantia');

            $oticaos->valor_total = str_replace(",", ".", str_replace(".", "", $request->input('valor_total')));
            $oticaos->pago = str_replace(",", ".", str_replace(".", "", $request->input('pago')));
            if (empty($request->input('pago'))) {
                $oticaos->pago = 0.00;
            }


            $oticaos->a_pagar = str_replace(",", ".", str_replace(".", "", $request->input('a_pagar')));
            $oticaos->vendedor = $request->input('vendedor');
            $oticaos->status = 'A';

            if (strlen($request->input('confirmado_lab'))) {
                $oticaos->confirmado_lab = $request->input('confirmado_lab');
            }
            #print_r($oticaos);
            #exit();
            if ($oticaos->a_pagar > 0) {
                $oticaos->baixa_status = "A";
            } else {
                $oticaos->baixa_status = "F";
            }

            $oticaos->save();

            return redirect()->route('index');
        }
    }
    public function Delete(Request $request, $id)
    {
        $usuario = ($request->session()->get("logado"));


        $oticaos = OticaOS::find($id);
        if ($usuario["id_empresa"] == $oticaos->id_empresa) {
            $oticaos->delete();
        }

        return redirect()->route('index');
    }

    public function exportar(Request $request)
    {
        ini_set('memory_limit', '512M');
        return Excel::download(new OSExport($request), 'os.xlsx');
    }

    public function PDF(Request $request, $id)
    {
        $usuario = ($request->session()->get("logado"));
        $oticaos = OticaOS::find($id);
        if ($oticaos->id_filial != null) {
            $filial = Filial::find($oticaos->id_filial);
        } else {
            $filial = Filial::find(1);
        }
        response()->view('os.pdf', ['oticas' => $oticaos, 'filial' => $filial]);

        if ($usuario["id_empresa"] == $oticaos->id_empresa) {
            $pdf = App::make('dompdf.wrapper');
            $pdf->setPaper([0, 0, 481.89, 841.89], 'portrait'); // 17cm x 29.7cm (A4)
            $pdf->loadView('os.pdf', ['oticas' => $oticaos, 'filial' => $filial]);
            return $pdf->stream();
        } else {
            return redirect()->route('403');
        }
    }

    public function PDFCliente(Request $request, $id)
    {
        $usuario = ($request->session()->get("logado"));
        $oticaos = OticaOS::find($id);

        if ($oticaos->id_filial != null) {
            $filial = Filial::find($oticaos->id_filial);
        } else {
            $filial = Filial::find(1);
        }
        response()->view('os.pdf', ['oticas' => $oticaos, 'filial' => $filial]);

        if ($usuario["id_empresa"] == $oticaos->id_empresa) {
            $pdf = App::make('dompdf.wrapper');
            $pdf->loadView('os.pdf_cliente', ['oticas' => $oticaos, 'filial' => $filial]);
            return $pdf->stream();
        } else {
            return redirect()->route('403');
        }
    }


    #Módulo de Baixa
    public function IndexBaixa(Request $request)
    {
        $usuario = ($request->session()->get("logado"));

        // Carrega filiais para o filtro
        $filiais = Filial::pluck('nome', 'id');

        $os = OticaOS::where("baixa_status", "A")
            ->where('a_pagar', '>', 0);

        // Filtro por pesquisa (nome ou ID)
        if ($request->has('search') && !empty($request->input('search'))) {
            $os->where(function ($query) use ($request) {
                $query->where('nome', 'Like', '%' . $request->input('search') . '%')
                    ->orWhere('id', $request->input('search'));
            });
        }

        // Filtro por período de data de emissão (formato HTML5: yyyy-mm-dd)
        if ($request->has('data_inicio') && !empty($request->input('data_inicio'))) {
            $os->where('dt_emissao', '>=', $request->input('data_inicio'));
        }

        if ($request->has('data_fim') && !empty($request->input('data_fim'))) {
            $os->where('dt_emissao', '<=', $request->input('data_fim'));
        }

        // Filtro por loja/filial
        if ($request->has('id_filial') && !empty($request->input('id_filial'))) {
            $os->where('id_filial', $request->input('id_filial'));
        }

        // Filtro por vendedor usando LIKE
        if ($request->has('vendedor') && !empty($request->input('vendedor'))) {
            $os->where('vendedor', 'LIKE', '%' . $request->input('vendedor') . '%');
        }

        $os->orderBy('dt_emissao', 'desc');

        $os = $os->where("id_empresa", $usuario["id_empresa"])->paginate(15);

        return response()->view('os.baixa.index', [
            'os' => $os,
            'filiais' => $filiais
        ]);
    }

    public function Baixa(Request $request, $id)
    {
        $usuario = ($request->session()->get("logado"));
        $oticaos = OticaOS::find($id);

        if ($usuario["id_empresa"] == $oticaos->id_empresa) {
            return response()->view('os.baixa.edit', ['oticas' => $oticaos]);
        } else {
            return redirect()->route('403');
        }
    }

    public function BaixaAction(Request $request, $id)
    {
        $rules = array(
            "baixa_data" => "required",
            "baixa_valor" => "required"
        );

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->route('baixa', array('id' => $id))->withErrors($validator)->withInput();
        } else {
            $oticaos = OticaOS::find($id);
            $oticaos->baixa_data = $request->input('baixa_data');
            $oticaos->baixa_observacao = $request->input('baixa_observacao');
            $oticaos->baixa_valor = str_replace(",", ".", str_replace(".", "", $request->input('baixa_valor')));
            $oticaos->baixa_status = "F";

            $oticaos->save();

            return redirect()->route('index_baixa');
        }
    }

    private function applyFilters($query, $request, $usuario)
    {
        // Filtro por pesquisa (nome ou ID)
        if ($request->has('search') && !empty($request->input('search'))) {
            $query->where(function ($q) use ($request) {
                return $q->where('oticasos.nome', 'Like', '%' . $request->input('search') . '%')
                    ->orWhere('oticasos.id', $request->input('search'));
            });
        }

        // Filtro por período de data de emissão
        if ($request->has('data_inicio') && !empty($request->input('data_inicio'))) {
            $query->where('dt_emissao', '>=', $request->input('data_inicio'));
        }

        if ($request->has('data_fim') && !empty($request->input('data_fim'))) {
            $query->where('dt_emissao', '<=', $request->input('data_fim'));
        }

        // Filtro por loja/filial
        if ($request->has('id_filial') && !empty($request->input('id_filial'))) {
            $query->where('id_filial', $request->input('id_filial'));
        }

        // Filtro por vendedor
        if ($request->has('vendedor') && !empty($request->input('vendedor'))) {
            $query->where('vendedor', 'LIKE', '%' . $request->input('vendedor') . '%');
        }

        // Filtro por período de data de previsão de entrega
        if ($request->has('dt_prev_entrega_inicio') && !empty($request->input('dt_prev_entrega_inicio'))) {
            $query->where('dt_prev_entrega', '>=', $request->input('dt_prev_entrega_inicio'));
        }

        if ($request->has('dt_prev_entrega_fim') && !empty($request->input('dt_prev_entrega_fim'))) {
            $query->where('dt_prev_entrega', '<=', $request->input('dt_prev_entrega_fim'));
        }

        // Filtra por empresa do usuário
        $query->where("id_empresa", $usuario["id_empresa"]);
    }
}

