<html>
<?php
    $longe = json_decode($oticas->longe);
    $perto = json_decode($oticas->perto);
?>

<head>
  <style type="text/css">
    @page {
      size: 8.5in 11in;
      margin: .5in .3in;
    }
    body {

      font-family: Arial, sans-serif;
      font-weight: normal;
      font-size: 10pt;
      margin: 0px;
      padding: 0px;
    }
    .MsoTableGrid tr td { padding: 5px; }

  </style>
</head>

<body lang="PT-BR">

<div>

<table width="100%">
  <tr>
  <td>
    <img src="{{env("URL_LOGO")}}" style="width:70pt" class="log" />
  </td>
  <td >
  <b>O.S.</b> <span style="text-decoration:underline;"> {{$oticas->id}} </span>
  </td>
 </tr>
</table>

{{env('TELEFONE_LOJA')}} | {{env('ENDERECO_LOJA')}}


<table width="100%">
  <tr>
    <td width=100%>
      Data de Emissão:
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{ $oticas->dt_emissao}}
    </td>
    <td width=100%>
      Previsão de Entrega:
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{ $oticas->dt_prev_entrega}}
    </td>
  </tr>
</table>
<br />

<table class="MsoTableGrid" border="1" cellspacing=0 cellpadding=0
 style='border-collapse:collapse;border:none; width: 100%'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=86 rowspan=3 style="border:none; border-right: 1pt solid;">
  LONGE
  </td>
  <td valign=top >

  </td>
  <td valign=top >
  ESF
  </td>
  <td valign=top >
  CIL
  </td>
  <td valign=top >
  EIXO
  </td>
  <td valign=top>
  DNP
  </td>
  <td valign=top>
  ALTURA
  </td>
 </tr>
 <tr style='mso-yfti-irow:1'>
  <td>
  OD
  </td>
  <td>
    @if( strlen($longe->od->esf) > 1 )
        {{$longe->od->esf}}
    @endif
  </td>
  <td>
      <?php if( substr($longe->od->cil, 0,1) != "-"  && !empty($longe->oe->cil)) { echo "-"; } ?>{{ $longe->od->cil }}
  </td>
  <td>
    {{ $longe->od->eixo }}<?php if(!empty($longe->od->eixo)) {echo "º";} ?>
  </td>
  <td>
    {{ $longe->od->dnp }}
  </td>
  <td>
    {{ $longe->od->altura }}
  </td>
 </tr>
 <tr style='mso-yfti-irow:2'>
  <td>
  OE
  </td>
  <td>
      @if( strlen($longe->oe->esf) > 1 )
          {{ $longe->oe->esf }}
      @endif

  </td>
  <td>
      <?php if( substr($longe->oe->cil, 0,1) != "-"  && !empty($longe->oe->cil)) { echo "-"; } ?>{{ $longe->oe->cil }}
  </td>
  <td>
    {{ $longe->oe->eixo }}<?php if(!empty($longe->oe->eixo)) {echo "º";} ?>
  </td>
  <td>
    {{ $longe->oe->dnp }}
  </td>
  <td>
    {{ $longe->oe->altura }}
  </td>
 </tr>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=86 rowspan=3 style="border:none; border-right: 1pt solid;">
  PERTO
  </td>
  <td valign=top >

  </td>
  <td valign=top >
  ESF
  </td>
  <td valign=top >
  CIL
  </td>
  <td valign=top >
  EIXO
  </td>
  <td valign=top>
  DNP
  </td>
  <td valign=top>
  ALTURA
  </td>
 </tr>
 <tr style='mso-yfti-irow:1'>
  <td>
  OD
  </td>
  <td>
      @if( strlen($perto->od->esf) > 1 )
          {{$perto->od->esf}}
      @endif
  </td>
  <td>
      <?php if( substr($perto->od->cil, 0,1) != "-" && !empty($perto->od->cil) ) { echo "-"; } ?>{{ $perto->od->cil }}
  </td>
  <td>
    {{ $perto->od->eixo }}<?php if(!empty($perto->od->eixo)) {echo "º";} ?>
  </td>
  <td>
    {{ $perto->od->dnp }}
  </td>
  <td>
    {{ $perto->od->altura }}
  </td>
 </tr>
 <tr style='mso-yfti-irow:2'>
  <td>
  OE
  </td>
  <td>
      @if( strlen($perto->oe->esf ) > 1 )
          {{ $perto->oe->esf }}
      @endif

  </td>
  <td>
    <?php if( substr($perto->oe->cil, 0,1) != "-" && !empty($perto->oe->cil)) { echo "-"; } ?>{{ $perto->oe->cil }}
  </td>
  <td>
    {{ $perto->oe->eixo }}<?php if(!empty($perto->oe->eixo)) {echo "º";} ?>
  </td>
  <td>
    {{ $perto->oe->dnp }}
  </td>
  <td>
    {{ $perto->oe->altura }}
  </td>
 </tr>
</table>
<table width="100%">
  <tr>
    <td valign="middle">
      Adição:
    </td>
    <td style="border-bottom: 1pt solid;">
      {{$oticas->adicao}}
    </td>
  </tr>
  <tr>
    <td valign="middle">
      Armação:
    </td>
    <td style="border-bottom: 1pt solid;">
      {{$oticas->armacao}}
    </td>
  </tr>
  <tr>
    <td valign="middle">
      Lentes:
    </td>
    <td style="border-bottom: 1pt solid;">
      {{$oticas->lentes}}
    </td>
  </tr>
  <tr>
    <td valign="middle">
      Obs:
    </td>
    <td style="border-bottom: 1pt solid;">
      {{$oticas->obs}}
    </td>
  </tr>
  <tr>
    <td valign="middle">
      Médico:
    </td>
    <td style="border-bottom: 1pt solid; width:100%">
      {{$oticas->medico}}
    </td>
  </tr>
</table>
<br />
<p><b>FORMAS DE PAGAMENTO</b></p>
<?php $pagamento = $oticas->forma_de_pagamento; ?>
<table width="100%">
 <tr>
  <td>
  [<span>@if($pagamento == "d") x @else &nbsp;&nbsp;  @endif</span>] DINHEIRO
  </td>
  <td>
  [<span>@if($pagamento == "v") x @else &nbsp;&nbsp; @endif </span>] VISA
  </td>
  <td>
  [<span>@if($pagamento == "o") x @else &nbsp;&nbsp; @endif </span>] OUTROS
  </td>
 </tr>
 <tr>
  <td>
  [<span>@if($pagamento == "c") x @else &nbsp;&nbsp; @endif </span>] CHEQUE
  </td>
  <td>
  [<span>@if($pagamento == "m") x @else &nbsp;&nbsp; @endif </span>] MASTER
  </td>
  <td>

  </td>
 </tr>
</table>
<br />
<center>--------------------------------------------------------------------------------------------------------------------------------------</center>
<br />
<table width="100%">
  <tr>
  <td>
    <img src="{{env("URL_LOGO")}}" style="width:70pt" class="log" />
  </td>
  <td >
    <b>O.S.</b> <span style="text-decoration:underline;"> {{$oticas->id}} </span>
  </td>
 </tr>
</table>

<small style="font-size: 10pt;">{{env('TELEFONE_LOJA')}} | {{env('ENDERECO_LOJA')}}</small>
<table width="100%">
  <tr>
    <td valign="middle">
      Nome:
    </td>
    <td style="border-bottom: 1pt solid;">
      {{ $oticas->nome}}
    </td>
  </tr>
  <tr>
    <td valign="middle">
      Endereço:
    </td>
    <td style="border-bottom: 1pt solid;">
      {{ $oticas->endereco}}
    </td>
  </tr>
  <tr>
    <td valign="middle">
      Fones:
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{ $oticas->fones}}
    </td>
  </tr>
</table>
<table width="100%">
  <tr>
    <td width="100%">
      Data de Emissão:
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{ $oticas->dt_emissao}}
    </td>
    <td width="100%">
      Previsão de Entrega:
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{ $oticas->dt_prev_entrega}}
    </td>
  </tr>
</table>

<table width="100%">
  <tr>
    <td style="width: 100%">
      Valor Total:
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{$oticas->valor_total}}
    </td>
    <td style="width: 100%">
      Pago: R$
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{$oticas->pago}}
    </td>
    <td style="width: 100%">
      À pagar: R$
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{$oticas->a_pagar}}
    </td>
  </tr>
</table>
<br />
<table width="100%">
  <tr>
    <td>
      Ass:
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">

    </td>
    <td>
      Vendedor(a):
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{$oticas->vendedor}}
    </td>
  </tr>
</table>
<br />
<p style="font-size: 10pt">
  Estou ciente que as mercadorias no prazo de 180 dias após a
  data da entrega, serão doados.
</p>
  <p align=center style='text-align:center;font-size: 10pt'>{{env('EMAIL_LOJA')}}</p>
<p>
    {{env('TELEFONE_LOJA')}} | {{env('ENDERECO_LOJA')}}
</p>
</div>

</body>

</html>
