<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Despesa extends Model {

	protected $table = 'despesas';
	public function getDtvencimentoAttribute($value)
	{
		$data = implode("/",array_reverse(explode("-",$value)));
		//return "10-10-1000";
		return ($data);
	}
	public function getValorAttribute($value)
	{
		$data = number_format($value, 2, ',', '.');
		return ($data);
	}

	public static function getNomeSituacao($situacao){
		if( $situacao == "A" ){
			return "Atrasado";
		}

		if( $situacao == "E" ){
			return "Pendente";
		}

		if( $situacao == "P" ){
			return "Pago";
		}
	}
	public static function formatValor($value)
	{
		$data = number_format($value, 2, ',', '.');
		return ($data);
	}
	public static function formatData($value)
	{
		$data = implode("/",array_reverse(explode("-",$value)));
		//return "10-10-1000";
		return ($data);
	}
}
