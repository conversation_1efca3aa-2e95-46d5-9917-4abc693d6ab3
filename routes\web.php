<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Controller;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\OSController;
use App\Http\Controllers\DespesaController;
use App\Http\Controllers\RelatorioController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', [HomeController::class, 'login'])->name('login');
Route::post('/', [HomeController::class, 'LoginAction'])->name('loginAction');


Route::middleware(['autenticate'])->group(function () {
    /* OS */
    Route::get('/index', [OSController::class, 'Index'])->name('index');

    Route::get('/add', [OSController::class, 'Add'])->name('add');
    Route::post('/add', [OSController::class, 'AddAction']);
    Route::get('/edit/{id}', [OSController::class, 'Edit'])->name('edit');
    Route::post('/edit/{id}', [OSController::class, 'EditAction']);
    Route::get('/delete/{id}', [OSController::class, 'Delete'])->name("remove");
    Route::get('/duplicar/{id}', [OSController::class, 'Duplicar'])->name('duplicar');

    /* PDF */
    Route::get('/pdf/{id}', [OSController::class, 'PDF'])->name('pdf');
    Route::get('/pdf_cliente/{id}', [OSController::class, 'PDFCliente'])->name('pdf_cliente');

    /* BAIXA */
    Route::get('/index_baixa', [OSController::class, 'IndexBaixa'])->name('index_baixa');
    Route::get('/baixa/{id}', [OSController::class, 'Baixa'])->name('baixa');
    Route::post('/baixa/{id}', [OSController::class, 'BaixaAction']);


    /* RELATORIO */
    Route::get('/rel_forma', [RelatorioController::class, 'RelatorioFormas'])->name('rel_forma');
    Route::get('/rel_receitadespesa', [RelatorioController::class, 'RelatorioReceitaDespesa'])->name('rel_receitadespesa');
    Route::get('/rel_geral', [RelatorioController::class, 'RelatorioGeral'])->name('rel_geral');

    Route::get('/403', array('as' => '403', function () {
        return "403 - Desautorizado";
    }));

    /* LOGOUT */
    Route::get('/logout', [HomeController::class, 'Logout'])->name('logout');






    Route::middleware(['admin'])->group(function () {
        /* EXPORT */
        Route::get('/exportar', [OSController::class, 'exportar'])->name('exportar');

        /* DESPESAS */
        Route::prefix('despesas')->name('despesas.')->group(function () {
            Route::get('/', [DespesaController::class, 'Index'])->name('index');
            Route::get('/add', [DespesaController::class, 'Add'])->name('add');
            Route::post('/add', [DespesaController::class, 'AddAction']);
            Route::get('/edit/{id}', [DespesaController::class, 'Edit'])->name('edit');
            Route::post('/edit/{id}', [DespesaController::class, 'EditAction']);
            Route::get('/delete/{id}', [DespesaController::class, 'Delete'])->name('remove');
        });
    });
});
