<?php

namespace App\Exports;

use App\Models\OticaOS;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromQuery;

use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class OSExport implements FromQuery, WithHeadings, WithMapping, WithChunkReading
{
    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function chunkSize(): int
    {
        return 1000;
    }

    public function query()
    {
        $request = $this->request;
        $usuario = ($request->session()->get("logado"));

        $os = OticaOS::query()->select('oticasos.*', 'filiais.nome as filial_nome')
            ->leftJoin('filiais', 'oticasos.id_filial', '=', 'filiais.id');

        if ($request->has('search') && !empty($request->input('search'))) {
            $os->where(function ($query) use ($request) {
                return $query->where('oticasos.nome', 'Like', '%' . $request->input('search') . '%')
                    ->orWhere('oticasos.id', $request->input('search'));
            });
        }

        if ($request->has('data_inicio') && !empty($request->input('data_inicio'))) {
            $os->where('dt_emissao', '>=', $request->input('data_inicio'));
        }

        if ($request->has('data_fim') && !empty($request->input('data_fim'))) {
            $os->where('dt_emissao', '<=', $request->input('data_fim'));
        }

        if ($request->has('id_filial') && !empty($request->input('id_filial'))) {
            $os->where('id_filial', $request->input('id_filial'));
        }

        if ($request->has('vendedor') && !empty($request->input('vendedor'))) {
            $os->where('vendedor', 'LIKE', '%' . $request->input('vendedor') . '%');
        }

        if ($request->has('valor_total') && !empty($request->input('valor_total'))) {
            $os->where('valor_total', '>=', $request->input('valor_total'));
        }

        if ($request->has('dt_prev_entrega_fim') && !empty($request->input('dt_prev_entrega_fim'))) {
            $os->where('dt_prev_entrega', '<=', $request->input('dt_prev_entrega_fim'));
        }

        $os->where("id_empresa", $usuario["id_empresa"]);

        return $os;
    }

    public function headings(): array
    {
        return [
            'Telefone',
            'Nome do Cliente',
            'Armação',
            'Lente',
            'Valor Total',
        ];
    }

    public function map($os): array
    {
        return [
            $os->fones,
            $os->nome,
            $os->armacao,
            $os->lentes,
            $os->valor_total,
        ];
    }
}
