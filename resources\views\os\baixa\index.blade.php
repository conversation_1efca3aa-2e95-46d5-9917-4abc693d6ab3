@extends('template.master')
@section('sidebar')


@stop
@section('content')

<style>
    .filtros-container {
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    }

    .filtros-titulo {
        color: #374151;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 2px solid #f3f4f6;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .filtros-titulo i {
        color: #6b7280;
    }

    .form-row-custom {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        align-items: end;
    }

    .form-group-filtro {
        display: flex;
        flex-direction: column;
    }

    .form-group-filtro label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        font-size: 14px;
        color: #374151;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .form-group-filtro label i {
        color: #6b7280;
        font-size: 13px;
    }

    .form-control-filtro {
        width: 100%;
        height: 40px;
        padding: 0 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        background: #ffffff;
        font-size: 14px;
        color: #374151;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .form-control-filtro:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-control-filtro::placeholder {
        color: #9ca3af;
    }

    .periodo-grupo {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        gap: 8px;
        align-items: center;
    }

    .periodo-ate {
        color: #6b7280;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
    }

    .btn-pesquisar-custom {
        background: #3b82f6;
        border: 1px solid #3b82f6;
        color: white;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 6px;
        justify-content: center;
    }

    .btn-pesquisar-custom:hover {
        background: #2563eb;
        border-color: #2563eb;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .btn-pesquisar-custom:active {
        transform: translateY(1px);
    }

    .form-actions {
        grid-column: 1 / -1;
        display: flex;
        justify-content: flex-end;
        margin-top: 8px;
    }

    @media (max-width: 768px) {
        .form-row-custom {
            grid-template-columns: 1fr;
        }

        .periodo-grupo {
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .periodo-ate {
            display: none;
        }

        .form-actions {
            justify-content: stretch;
        }

        .btn-pesquisar-custom {
            width: 100%;
        }
    }

    @media (min-width: 1200px) {
        .form-row-custom {
            grid-template-columns: repeat(3, 1fr);
        }
    }
</style>

<div class="filtros-container">
    <h4 class="filtros-titulo">
        <i class="fa fa-search"></i>
        Filtros de Pesquisa
    </h4>

    <form method="GET" action="{{ route('index_baixa') }}">
        <div class="form-row-custom">
            <div class="form-group-filtro">
                <label for="search">
                    <i class="fa fa-user"></i>
                    Cliente/Código
                </label>
                <input type="text"
                    class="form-control-filtro"
                    id="search"
                    name="search"
                    value="{{ Request::input('search')}}"
                    placeholder="Nome do cliente ou código da O.S.">
            </div>

            <div class="form-group-filtro">
                <label for="id_filial">
                    <i class="fa fa-building"></i>
                    Loja
                </label>
                <select class="form-control-filtro" id="id_filial" name="id_filial">
                    <option value="">Todas as lojas</option>
                    @foreach($filiais as $id => $nome)
                    <option value="{{ $id }}" {{ Request::input('id_filial') == $id ? 'selected' : '' }}>
                        {{ $nome }}
                    </option>
                    @endforeach
                </select>
            </div>

            <div class="form-group-filtro">
                <label for="vendedor">
                    <i class="fa fa-id-badge"></i>
                    Vendedor
                </label>
                <input type="text"
                    class="form-control-filtro"
                    id="vendedor"
                    name="vendedor"
                    value="{{ Request::input('vendedor')}}"
                    placeholder="Nome do vendedor">
            </div>

            <div class="form-group-filtro">
                <label>
                    <i class="fa fa-calendar"></i>
                    Período (Dt. Emissão)
                </label>
                <div class="periodo-grupo">
                    <input type="date"
                        class="form-control-filtro"
                        name="data_inicio"
                        value="{{ Request::input('data_inicio') }}">
                    <span class="periodo-ate">até</span>
                    <input type="date"
                        class="form-control-filtro"
                        name="data_fim"
                        value="{{ Request::input('data_fim') }}">
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-pesquisar-custom">
                    <i class="fa fa-search"></i>
                    Pesquisar
                </button>

            </div>
        </div>
    </form>
</div>


@if(count($os) > 0)
<table id="general-table" class="table table-striped table-bordered">
    <thead>
        <tr>
            <th>Código da O.S.</th>
            <th width="40%">Nome</th>
            <th>Loja/Vendedor</th>
            <th>Data de Emissão</th>
            <th>Confirmação Lab.</th>
            <th>À pagar</th>
            <th style="width: 200px;" class="text-center">Opções</th>
        </tr>
    </thead>
    <tbody>
        @foreach( $os as $product )
        <tr>
            <td>{{ $product->id }}</td>
            <td>
                {{ strtoupper( $product->nome ) }}
                @if(!empty($product->fones))
                @php($primeiro_fone = trim(preg_split('/[\\,\/]/', $product->fones)[0]))
                @if(!empty($primeiro_fone))
                <br>
                <a href="https://api.whatsapp.com/send?phone=55{{ \App\Helpers\Helpers::sanitizePhone($primeiro_fone) }}" target="_blank">
                    <i class="fa fa-whatsapp"></i> {{ $product->fones }}
                </a>
                @endif
                @endif
                <br>
                <small>
                    @if(!empty($product->armacao))
                    <span class="d-block"><strong class="fw-bold">Armação:</strong> {{ strtolower($product->armacao) }}
                        @endif
                        @if(!empty($product->lentes))
                        <br />
                        <strong class="fw-bold">Lentes:</strong> {{ strtolower($product->lentes) }}
                        @endif
                        @if(!empty($product->valor_total))
                        <br />
                        <span class="d-block"><strong class="fw-bold">Total:</strong> R$ {{$product->valor_total}}</span>
                        @endif

                </small>
            </td>
            <td>
                @if($product->filial)
                <strong>{{ $product->filial->nome }}</strong>
                @else
                <em>Loja não informada</em>
                @endif
                @if(!empty($product->vendedor))
                <br>{{ $product->vendedor }}
                @endif
            </td>
            <td>
                {{ $product->dt_emissao }}
                <br>
                @if(!empty($product->dt_prev_entrega))
                <small><strong class="fw-bold">Prev: </strong>{{ $product->dt_prev_entrega }}</small>
                @endif
            </td>
            <td>{{ $product->confirmado_lab }}</td>
            <td>{{ $product->a_pagar }}</td>
            <td>
                <a href="{{ route('baixa', $product->id) }}" title="Dar baixa na O.S." class="btn btn-info"><i class="fa fa-share"></i> Baixa</a>
            </td>
        </tr>
        @endforeach
    </tbody>
</table>

{{ $os->appends(Request::input())->links() }}
@else
<p class="well"> Nenhum registro </p>
@endif
@stop