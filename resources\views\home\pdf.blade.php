<html>
<?php
    $longe = json_decode($oticas->longe);
    $perto = json_decode($oticas->perto);
?>

<head>
  <style type="text/css">
    body {
      font-family: Arial, sans-serif;
      font-weight: normal;
    }
    .MsoTableGrid tr td {padding: 5px;}

  </style>
</head>

<body lang=PT-BR>

<div>

<table width=100%>
  <tr>
  <td>
    <img src="{{Request::root()}}/FinezzaOtica.png" style="width:150pt" class="log" />
  </td>
  <td >
  <b>O.S.</b> <span style="text-decoration:underline;"> {{$oticas->id}} </span>
  </td>
 </tr>
</table>

<small style="font-size: 8pt;">(61) 3021-2370 / 8563-2639 / 9327-0844 | QNN 20 Conjunto "P" Casa 57 - Ceilândia Sul</small>


<table width="100%">
  <tr>
    <td width=100%>
      Data de Emissão:
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{ $oticas->dt_emissao}}
    </td>
    <td width=100%>
      Previsão de Entrega:
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{ $oticas->dt_prev_entrega}}
    </td>
  </tr>
</table>
<br />

<table class=MsoTableGrid border=1 cellspacing=0 cellpadding=0
 style='border-collapse:collapse;border:none; width: 100%'>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=86 rowspan=3 style="border:none; border-right: 1pt solid;">
  LONGE
  </td>
  <td valign=top >

  </td>
  <td valign=top >
  ESF
  </td>
  <td valign=top >
  CIL
  </td>
  <td valign=top >
  EIXO
  </td>
  <td valign=top>
  DNP
  </td>
  <td valign=top>
  ALTURA
  </td>
 </tr>
 <tr style='mso-yfti-irow:1'>
  <td>
  OD
  </td>
  <td>
    {{ $longe->od->esf }}
  </td>
  <td>
    {{ $longe->od->cil }}
  </td>
  <td>
    {{ $longe->od->eixo }}
  </td>
  <td>
    {{ $longe->od->dnp }}
  </td>
  <td>
    {{ $longe->od->altura }}
  </td>
 </tr>
 <tr style='mso-yfti-irow:2'>
  <td>
  OE
  </td>
  <td>
    {{ $longe->oe->esf }}
  </td>
  <td>
    {{ $longe->oe->cil }}
  </td>
  <td>
    {{ $longe->oe->eixo }}
  </td>
  <td>
    {{ $longe->oe->dnp }}
  </td>
  <td>
    {{ $longe->oe->altura }}
  </td>
 </tr>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
  <td width=86 rowspan=3 style="border:none; border-right: 1pt solid;">
  PERTO
  </td>
  <td valign=top >

  </td>
  <td valign=top >
  ESF
  </td>
  <td valign=top >
  CIL
  </td>
  <td valign=top >
  EIXO
  </td>
  <td valign=top>
  DNP
  </td>
  <td valign=top>
  ALTURA
  </td>
 </tr>
 <tr style='mso-yfti-irow:1'>
  <td>
  OD
  </td>
  <td>
    {{ $perto->od->esf }}
  </td>
  <td>
    {{ $perto->od->cil }}
  </td>
  <td>
    {{ $perto->od->eixo }}
  </td>
  <td>
    {{ $perto->od->dnp }}
  </td>
  <td>
    {{ $perto->od->altura }}
  </td>
 </tr>
 <tr style='mso-yfti-irow:2'>
  <td>
  OE
  </td>
  <td>
    {{ $perto->oe->esf }}
  </td>
  <td>
    {{ $perto->oe->cil }}
  </td>
  <td>
    {{ $perto->oe->eixo }}
  </td>
  <td>
    {{ $perto->oe->dnp }}
  </td>
  <td>
    {{ $perto->oe->altura }}
  </td>
 </tr>
</table>
<table width="100%">
  <tr>
    <td valign="middle">
      Adição:
    </td>
    <td style="border-bottom: 1pt solid;">
      {{$oticas->adicao}}
    </td>
  </tr>
  <tr>
    <td valign="middle">
      Armação:
    </td>
    <td style="border-bottom: 1pt solid;">
      {{$oticas->armacao}}
    </td>
  </tr>
  <tr>
    <td valign="middle">
      Lentes:
    </td>
    <td style="border-bottom: 1pt solid;">
      {{$oticas->lentes}}
    </td>
  </tr>
  <tr>
    <td valign="middle">
      Obs:
    </td>
    <td style="border-bottom: 1pt solid;">
      {{$oticas->obs}}
    </td>
  </tr>
  <tr>
    <td valign="middle">
      Médico:
    </td>
    <td style="border-bottom: 1pt solid; width:100%">
      {{$oticas->medico}}
    </td>
  </tr>
</table>
<br />
<p><b>FORMAS DE PAGAMENTO</b></p>
<?php $pagamento = $oticas->forma_de_pagamento; ?>
<table width="100%">
 <tr>
  <td>
  [<span>@if($pagamento == "d") x @else &nbsp;&nbsp;  @endif</span>] DINHEIRO
  </td>
  <td>
  [<span>@if($pagamento == "v") x @else &nbsp;&nbsp; @endif </span>] VISA
  </td>
  <td>
  [<span>@if($pagamento == "o") x @else &nbsp;&nbsp; @endif </span>] OUTROS
  </td>
 </tr>
 <tr>
  <td>
  [<span>@if($pagamento == "c") x @else &nbsp;&nbsp; @endif </span>] CHEQUE
  </td>
  <td>
  [<span>@if($pagamento == "m") x @else &nbsp;&nbsp; @endif </span>] MASTER
  </td>
  <td>

  </td>
 </tr>
</table>
--------------------------------------------------------------------------------------------------------------------------------------
<br />
<br />

<table width="100%">
  <tr>
  <td>
    <img src="{{Request::root()}}/FinezzaOtica.png" style="width:150pt" class="log" />
  </td>
  <td >
  <b>O.S.</b> <span style="text-decoration:underline;"> {{$oticas->id}} </span>
  </td>
 </tr>
</table>

<small style="font-size: 10pt;">(61) 3021-2370 / 8563-2639 / 9327-0844 | QNN 20 Conjunto "P" Casa 57 - Ceilândia Sul</small>
<table width="100%">
  <tr>
    <td valign="middle">
      Nome:
    </td>
    <td style="border-bottom: 1pt solid;">
      {{ $oticas->nome}}
    </td>
  </tr>
  <tr>
    <td valign="middle">
      Endereço:
    </td>
    <td style="border-bottom: 1pt solid;">
      {{ $oticas->endereco}}
    </td>
  </tr>
  <tr>
    <td valign="middle">
      Fones:
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{ $oticas->fones}}
    </td>
  </tr>
</table>
<table width="100%">
  <tr>
    <td width=100%>
      Data de Emissão:
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{ $oticas->dt_emissao}}
    </td>
    <td width=100%>
      Previsão de Entrega:
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{ $oticas->dt_prev_entrega}}
    </td>
  </tr>
</table>

<table width="100%">
  <tr>
    <td style="width: 100%">
      Valor Total:
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{$oticas->valor_total}}
    </td>
    <td style="width: 100%">
      Pago: R$
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{$oticas->pago}}
    </td>
    <td style="width: 100%">
      À pagar: R$
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{$oticas->a_pagar}}
    </td>
  </tr>
</table>
<br />
<table width="100%">
  <tr>
    <td>
      Ass:
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">

    </td>
    <td>
      Vendedor(a):
    </td>
    <td style="border-bottom: 1pt solid; width: 100%">
      {{$oticas->vendedor}}
    </td>
  </tr>
</table>
<br />
<p style="font-size: 10pt">
  Estou ciente que as mercadorias no prazo de 180 dias após a
  data da entrega, serão doados.
</p>
  <p align=center style='text-align:center;font-size: 10pt'><EMAIL></p>
<p style="font-size: 8pt">
  (61) 3021-2370 / 8563-2639 / 9327-0844 | QNN 20 Conjunto "P" Casa 57 - Ceilândia Sul
</p>
</div>

</body>

</html>
