@extends('template.master')

@section('sidebar')



@stop

@section('content')

{{ Form::model($despesa,
    array(
        'enctype'                           =>  'multipart/form-data',
        'method'                            =>  'post',
        'class'                             =>  'form-horizontal form-bordered',
    ))
}}

@if ( $errors->count() > 0 )
<div class="alert alert-danger alert-dismissable">
    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
    <h4><i class="fa fa-times-circle"></i>
        <strong>
            Erros de preenchimento
        </strong>
    </h4>
    @foreach( $errors->all() as $message )
       {{ $message }}
        <br />
    @endforeach
</div>
@endif


<div class="form-group">
    <label for="os" class="col-sm-10 control-label">Código</label>
    <div class="col-sm-2">
        {{ Form::text( 'id', old('id'), array( 'class' => 'form-control', 'id'=>'id', 'placeholder'=>'O.S.', 'autocomplete'=>'off', 'disabled'=>'disabled', 'class'=>'form-control' )) }}
    </div>
</div>
<div class="form-group col-sm-6">
    <label for="dt_emissao" class="col-sm-4 control-label">Data de vencimento:</label>
    <div class="col-sm-8">
        {{ Form::text( 'dt_vencimento', old('dt_vencimento'), array( 'class' => 'form-control', 'id'=>'dt_emissao', 'maxlength'=>10, 'placeholder'=>'dd/mm/aaaa', 'autocomplete'=>'off', 'class'=>'data form-control' )) }}
    </div>
</div>
<br class="clearfix" style="clear: both" />
<div class="form-group">
    <label for="obs" class="col-sm-2 control-label">Descrição:</label>
    <div class="col-sm-10">
        {{ Form::text('descricao', old('descricao'), array( 'class' => 'form-control', 'id'=>'descricao', 'maxlength'=>1000, 'autocomplete'=>'off', 'class'=>'form-control' )) }}
    </div>
</div>

<!--VALORES-->

<div class="form-group col-sm-6">
    <label for="valor_total" class="col-sm-4 control-label">Valor Total:</label>
    <div class="col-sm-8">
       {{ Form::text('valor', old('valor'), array( 'class' => 'form-control', 'id'=>'valor', 'autocomplete'=>'off', 'class'=>'dinheiro form-control' )) }}
    </div>
</div>

<div class="form-group  col-sm-6">
    <label for="situacao" class="col-sm-2 control-label">Situação:</label>
    <div class="col-sm-10">
        <div class="col-sm-6">
            <div class="checkbox">
                <label>
                    {{ Form::radio('situacao', 'P') }} Pago
                </label>
            </div>
            <div class="checkbox">
                <label>
                    {{ Form::radio('situacao', 'A') }} Atrasado
                </label>
            </div>
            <div class="checkbox">
                <label>
                    {{ Form::radio('situacao', 'E') }} Pendente
                </label>
            </div>
        </div>
    </div>
</div>

<div class="form-group">
    <div class="col-sm-offset-2 col-sm-10">
        <button type="submit" class="btn btn-primary">Salvar</button>
        <a href="{{ route('despesas.index') }}" class="btn btn-default">Voltar</a>
    </div>
</div>


{{ Form::close() }}
@stop
