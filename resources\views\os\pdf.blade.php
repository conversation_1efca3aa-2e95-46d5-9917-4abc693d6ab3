<html>
<?php
$longe = json_decode($oticas->longe);
$perto = json_decode($oticas->perto);
//$fmt = new NumberFormatter('pt_BR', NumberFormatter::CURRENCY);
?>

<head>
  <style type="text/css">
    @page {
      size: 8.5in 11in;
      margin: .3in .3in;
    }

    body {
      width: 17cm !important;
      max-width: 17cm !important;
      font-family: Arial, sans-serif;
      font-weight: normal;
      font-size: 9pt;
      margin: 0px;
      padding: 0px;
    }

    .MsoTableGrid tr td {
      padding: 5px;
    }
  </style>
</head>

<body lang="PT-BR">

  <div>

    <table width="90%">
      <tr>
        <td width="90pt">
          <img src="{{$filial->id}}.jpg" style="width:70pt" class="log" />
        </td>
        <td width="60%">
          {{$filial->nome}}
          <br />
          <img src="whatsappicon.png" style="width:8pt" /> {{$filial->telefone}} | {{$filial->endereco}}
          <br />
          Próximo ao Subway
        </td>
        <td style="font-size: 20pt">
          <b>O.S.</b> <span style="text-decoration:underline;"> {{$oticas->id}} </span>
        </td>
      </tr>
    </table>
    <hr style="width:80%" />
    <table>
      <tr>
        <td colspan="2">
          <b>Nome:</b> {{ $oticas->nome }} - <b>Telefone:</b> {{ $oticas->fones }}
        </td>

        <td>
          <b>Vendedor(a):</b>
          {{$oticas->vendedor}}
        </td>
      </tr>

      <tr>
        <td>
          <b>Total:</b> R$ {{ $oticas->valor_total }}
        </td>
        <td style="text-align: center">
          <b>Pago:</b> R$ {{ $oticas->pago }}
        </td>
        <td>
          <b>À pagar:</b> R$ {{ $oticas->a_pagar }}
        </td>
      </tr>
    </table>
    <hr style="width:80%" />

    <table width="90%">
      <tr>
        <td width=100%>
          Data de Emissão:
        </td>
        <td style="border-bottom: 1pt solid; width: 100%">
          {{ $oticas->dt_emissao}}
        </td>
        <td width=100%>
          Previsão de Entrega:
        </td>
        <td style="border-bottom: 1pt solid; width: 100%">
          {{ $oticas->dt_prev_entrega}}
        </td>
      </tr>
    </table>
    <br />

    <table class="MsoTableGrid" border="1" cellspacing=0 cellpadding=0
      style='border-collapse:collapse;border:none; width: 90%; font-size: 12pt;'>
      <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
        <td width=46 rowspan=3 style="border:none; border-right: 1pt solid;">
          LONGE
        </td>
        <td valign=top width="30pt">

        </td>
        <td valign=top>
          ESF
        </td>
        <td valign=top>
          CIL
        </td>
        <td valign=top>
          EIXO
        </td>
        <td valign=top width="30pt">
          DNP
        </td>
        <td valign=top width="30pt">
          ALTURA
        </td>
      </tr>
      <tr style='mso-yfti-irow:1'>
        <td>
          OD
        </td>
        <td>
          @if( strlen($longe->od->esf) > 1 )
          {{$longe->od->esf}}
          @endif
        </td>
        <td>
          <?php if (substr($longe->od->cil, 0, 1) != "-"  && !empty($longe->oe->cil)) {
            echo "-";
          } ?>{{ $longe->od->cil }}
        </td>
        <td>
          {{ $longe->od->eixo }}<?php if (!empty($longe->od->eixo)) {
                                  echo "º";
                                } ?>
        </td>
        <td>
          {{ $longe->od->dnp }}
        </td>
        <td>
          {{ $longe->od->altura }}
        </td>
      </tr>
      <tr style='mso-yfti-irow:2'>
        <td>
          OE
        </td>
        <td>
          @if( strlen($longe->oe->esf) > 1 )
          {{ $longe->oe->esf }}
          @endif

        </td>
        <td>
          <?php if (substr($longe->oe->cil, 0, 1) != "-"  && !empty($longe->oe->cil)) {
            echo "-";
          } ?>{{ $longe->oe->cil }}
        </td>
        <td>
          {{ $longe->oe->eixo }}<?php if (!empty($longe->oe->eixo)) {
                                  echo "º";
                                } ?>
        </td>
        <td>
          {{ $longe->oe->dnp }}
        </td>
        <td>
          {{ $longe->oe->altura }}
        </td>
      </tr>
      <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes'>
        <td width=46 rowspan=3 style="border:none; border-right: 1pt solid;">
          PERTO
        </td>
        <td valign=top>

        </td>
        <td valign=top>
          ESF
        </td>
        <td valign=top>
          CIL
        </td>
        <td valign=top>
          EIXO
        </td>
        <td valign=top>
          DNP
        </td>
        <td valign=top>
          ALTURA
        </td>
      </tr>
      <tr style='mso-yfti-irow:1'>
        <td>
          OD
        </td>
        <td>
          @if( strlen($perto->od->esf) > 1 )
          {{$perto->od->esf}}
          @endif
        </td>
        <td>
          <?php if (substr($perto->od->cil, 0, 1) != "-" && !empty($perto->od->cil)) {
            echo "-";
          } ?>{{ $perto->od->cil }}
        </td>
        <td>
          {{ $perto->od->eixo }}<?php if (!empty($perto->od->eixo)) {
                                  echo "º";
                                } ?>
        </td>
        <td>
          {{ $perto->od->dnp }}
        </td>
        <td>
          {{ $perto->od->altura }}
        </td>
      </tr>
      <tr style='mso-yfti-irow:2'>
        <td>
          OE
        </td>
        <td>
          @if( strlen($perto->oe->esf ) > 1 )
          {{ $perto->oe->esf }}
          @endif

        </td>
        <td>
          <?php if (substr($perto->oe->cil, 0, 1) != "-" && !empty($perto->oe->cil)) {
            echo "-";
          } ?>{{ $perto->oe->cil }}
        </td>
        <td>
          {{ $perto->oe->eixo }}<?php if (!empty($perto->oe->eixo)) {
                                  echo "º";
                                } ?>
        </td>
        <td>
          {{ $perto->oe->dnp }}
        </td>
        <td>
          {{ $perto->oe->altura }}
        </td>
      </tr>
      <tr>
        <td style="border:none; ;"></td>
        <td style="border-top: 1pt solid;">Adição:</td>
        <td style="border-top: 1pt solid;">{{$oticas->adicao}}</td>
        <td style="border-top: 1pt solid;"></td>
        <td style="border-top: 1pt solid;"></td>
        <td style="border-top: 1pt solid;"></td>
        <td style="border-top: 1pt solid;"></td>
      </tr>
    </table>
    <table width="90%" style="font-size: 12pt; margin-top: 10pt">
      <tr>
        <td valign="middle" style="width: 48pt;">
          Lentes:
        </td>
        <td style="border-bottom: 1pt solid;">
          {{$oticas->lentes}}
        </td>
      </tr>
      <tr>
        <td valign="middle">
          Obs:
        </td>
        <td style="border-bottom: 1pt solid;">
          {{$oticas->obs}}
        </td>
      </tr>
      <tr>
        <td valign="middle">
          Armação:
        </td>
        <td style="border-bottom: 1pt solid;">
          [ @if(!$oticas->armacao_sem_garantia) x @else @endif] Loja [@if($oticas->armacao_sem_garantia) x @else @endif] Próprio - {{$oticas->armacao}}
        </td>
      </tr>
      @if($oticas->armacao_sem_garantia)
      <tr>
        <td valign="middle">
        </td>
        <td>
          [ x ] Escolhi lentes sem garantia de quebra/trincados
        </td>
      </tr>
      @endif

    </table>
    <br />
    <center>--------------------------------------------------------------------------------------------------------------------------------------</center>
    <br />

    <table width="90%">
      <tr>
        <td width="90pt">
          <img src="{{$filial->id}}.jpg" style="width:70pt" class="log" />
        </td>
        <td width="60%">
          {{$filial->nome}}
          <br />
          <img src="whatsappicon.png" style="width:8pt" /> {{$filial->telefone}} | {{$filial->endereco}}
        </td>
        <td style="font-size: 20pt">
          <b>O.S.</b> <span style="text-decoration:underline;"> {{$oticas->id}} </span>
        </td>
      </tr>
    </table>
    <hr  style="width:80%" />
    <table>
      <tr>
        <td colspan="3">
          <b>Nome:</b> {{ $oticas->nome }} - <b>Telefone:</b> {{ $oticas->fones }}
        </td>
      </tr>

      <tr>
        <td>
          <b>Total:</b> R$ {{ $oticas->valor_total }}
        </td>
        <td style="text-align: center">
          <b>Pago:</b> R$ {{ $oticas->pago }}
        </td>
        <td>
          <b>À pagar:</b> R$ {{ $oticas->a_pagar }}
        </td>
      </tr>
    </table>
    <hr  style="width:80%" />

    <table width="90%">
      <tr>
        <td valign="middle" width="100pt">
          Endereço:
        </td>
        <td style="border-bottom: 1pt solid;">
          {{ $oticas->endereco}}
        </td>
      </tr>
    </table>
    <table width="90%">
      <tr>
        <td width="100pt">
          Data de Emissão:
        </td>
        <td style="border-bottom: 1pt solid; width: 150pt">
          {{ $oticas->dt_emissao}}
        </td>
        <td width="100pt">
          Previsão de Entrega:
        </td>
        <td style="border-bottom: 1pt solid; width: 100%">
          {{ $oticas->dt_prev_entrega}}
        </td>
      </tr>
    </table>
    <table width="90%">
      <tr>
        <td valign="middle" width="100px">
          Armação:
        </td>
        <td style="border-bottom: 1pt solid;">
          [ @if(!$oticas->armacao_sem_garantia) x @else @endif] Loja [@if($oticas->armacao_sem_garantia) x @else @endif] Próprio - {{$oticas->armacao}}
        </td>
      </tr>
      @if($oticas->armacao_sem_garantia)
      <tr>
        <td valign="middle">
        </td>
        <td>
          [ x ] Escolhi lentes sem garantia de quebra/trincados
        </td>
      </tr>
      @endif
      <tr>
        <td valign="middle" width="100px">
          Lentes:
        </td>
        <td style="border-bottom: 1pt solid;">
          {{$oticas->lentes}}
        </td>
      </tr>
    </table>

    <br />
    <table width="90%">
      <tr>
        <td>
          Ass:
        </td>
        <td style="border-bottom: 1pt solid; width: 100%">

        </td>
        <td>
          Vendedor(a):
        </td>
        <td style="border-bottom: 1pt solid; width: 100%">
          {{$oticas->vendedor}}
        </td>
      </tr>
    </table>
    <br />
    <p style="font-size: 9pt">
      Não nos responsabilizamos por armações usadas/terceiros.
      <br />
      Estou ciente que as mercadorias no prazo de 180 dias após a
      data da entrega, serão doados.
      <br />
      A garantia para adaptação e/ou erro médico é válida por 90 dias, contados a partir da data constante no certificado. Marcas com garantia: Hoya, Essilor (Varilux), Zeiss e Freestyle.

      @if($oticas->armacao_sem_garantia)
      <br />
      Escolhi lentes sem garantia de quebra/trincados
      @endif
    </p>

  </div>

</body>

</html>