<?php

namespace App\Http\Controllers;


use App\Http\Controllers\Controller;
use App\Models\Usuario;
use Illuminate\Http\Request;

class HomeController extends Controller
{

    /*
	|--------------------------------------------------------------------------
	| Default Home Controller
	|--------------------------------------------------------------------------
	|
	| You may wish to use controllers instead of, or in addition to, Closure
	| based routes. That's great! Here is an example controller method to
	| get you started. To route to this controller, just add the route:
	|
	|	Route::get('/', 'HomeController@showWelcome');
	|
	*/

    public function Login(Request $request)
    {

        if ($request->session()->has("logado")) {
            return redirect()->route("index");
        }

        if ($request->session()->has("message")) {
            return response()->view("home.login", ["messsage" => $request->session()->get("message")]);
        } else {
            return response()->view("home.login");
        }
    }
    public function LoginAction(Request $request)
    {
        $usuario = Usuario::where("email", $request->input("email"))->where("senha", $request->input("senha"))->first();

        if ($usuario != null) {
            $request->session()->put("logado", $usuario->toArray());

            return redirect()->route('index');
        } else {
            $request->session()->put('message', 'Usuário ou senha inválido.');
            return back();
        }
    }

    public function Logout(Request $request)
    {
        $request->session()->forget("logado");
        return redirect()->route('login');
    }
}
