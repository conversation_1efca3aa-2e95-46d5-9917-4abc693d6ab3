@extends('template.master')

@section('sidebar')



@stop

@section('content')

{{ Form::model( $oticas,
    array(
        'enctype'                           =>  'multipart/form-data',
        'method'                            =>  'post',
        'class'                             =>  'form-horizontal form-bordered',
    ))
}}

@if ( $errors->count() > 0 )
<div class="alert alert-danger alert-dismissable">
    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
    <h4><i class="fa fa-times-circle"></i>
        <strong>
            Erros de preenchimento
        </strong>
    </h4>
    @foreach( $errors->all() as $message )
       {{ $message }}
        <br />
    @endforeach
</div>
@endif

<div class="form-group col-sm-6">
    <label for="dt_emissao" class="col-sm-3 control-label">O.S.:</label>
    <div class="col-sm-9">
        <label class="control-label" style="font-weight: normal">{{$oticas->id}}</label>
    </div>
</div>

<div class="form-group col-sm-6">
    <label for="dt_emissao" class="col-sm-3 control-label">Nome:</label>
    <div class="col-sm-9">
        <label class="control-label" style="font-weight: normal">{{$oticas->nome}}</label>
    </div>
</div>

<div class="form-group col-sm-6">
    <label for="dt_emissao" class="col-sm-3 control-label">Valor Total:</label>
    <div class="col-sm-9">
        <label class="control-label" style="font-weight: normal">{{$oticas->valor_total}}</label>
    </div>
</div>

<div class="form-group col-sm-6">
    <label for="dt_emissao" class="col-sm-3 control-label">Pago:</label>
    <div class="col-sm-9">
        <label class="control-label" style="font-weight: normal">{{$oticas->pago}}</label>
    </div>
</div>

<div class="form-group col-sm-6">
    <label for="dt_emissao" class="col-sm-3 control-label">À pagar:</label>
    <div class="col-sm-9">
        <label class="control-label" style="font-weight: normal">{{$oticas->a_pagar}}</label>
    </div>
</div>
<br class="clearfix" style="clear: both"/>
<br class="clearfix" style="clear: both"/>
<br class="clearfix" style="clear: both"/>
<br class="clearfix" style="clear: both"/>

<fieldset>
    <legend>Baixa da O.S.</legend>
    <br class="clearfix" style="clear: both"/>
    <div class="form-group col-sm-6">
        <label for="baixa_data" class="col-sm-3 control-label">Data:</label>
        <div class="col-sm-9">
            <input type="date" 
                   class="form-control" 
                   id="baixa_data" 
                   name="baixa_data" 
                   value="{{ old('baixa_data', date('Y-m-d')) }}" 
                   autocomplete="off">
        </div>
    </div>

    <div class="form-group col-sm-6">
        <label for="valor_total" class="col-sm-3 control-label">Valor Total:</label>
        <div class="col-sm-9">
            {{ Form::text('baixa_valor', old('baixa_valor'), array( 'class' => 'form-control', 'id'=>'baixa_valor', 'autocomplete'=>'off', 'class'=>'dinheiro form-control' )) }}
        </div>
    </div>
    <div class="form-group col-sm-12" >
        <label for="obs" class="col-sm-2 control-label">Obs.:</label>
        <div class="col-sm-8">
           {{ Form::textarea('baixa_observacao', old('baixa_observacao'), array( 'class' => 'form-control', 'id'=>'baixa_observacao', 'autocomplete'=>'off', 'class'=>'form-control' )) }}
        </div>
    </div>

<div class="form-group">
    <div class="col-sm-offset-2 col-sm-10">
        <button type="submit" class="btn btn-primary">Salvar</button>
        <a href="{{ route('index_baixa') }}" class="btn btn-default">Voltar</a>
    </div>
</div>
</fieldset>

{{ Form::close() }}
@stop
