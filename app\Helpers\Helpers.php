<?php

namespace App\Helpers;

class Helpers
{
    /**
     * Sanitiza um número de telefone, removendo todos os caracteres não numéricos.
     *
     * @param string|null $telefone
     * @return string
     */
    public static function sanitizePhone(?string $telefone): string
    {
        if (empty($telefone)) {
            return '';
        }
        return preg_replace('/[^0-9]/', '', $telefone);
    }
} 