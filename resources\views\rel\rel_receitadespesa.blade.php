@extends('template.master')
@section('sidebar')


@stop
@section('content')
    <?php
    if( isset($dados["receita"]) ){
        $receita = $dados["receita"];
    }else{
        $receita = 0;
    }

    if( isset($dados["despesa"][0]->Valor) ){
        $despesa = $dados["despesa"][0]->Valor;
    }else{
        $despesa = 0;
    }
    ?>

    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

    <script type="text/javascript">
        google.charts.load('current', {'packages':['corechart', 'bar']});
        google.charts.setOnLoadCallback(drawChart);
        function drawChart() {
            var data = google.visualization.arrayToDataTable([
                ['Data', 'Receita', 'Despesa', 'Lucro' ],
                ['{{App\Models\Despesa::formatData($dados["dataI"])}} - {{App\Models\Despesa::formatData($dados["dataF"])}}', {{$receita}}, {{$despesa}}, {{$receita-$despesa}}]
            ]);

            var options = {
                chart: {
                    title: 'Performace da empresa',
                    subtitle: 'Receita X Despesa X Dia',
                }
            };

            var chart = new google.charts.Bar(document.getElementById('columnchart_material'));

            chart.draw(data, options);
        }
    </script>


<div class="panel panel-default">
    <div class="panel-heading">Receita despesa</div>
    <div class="panel-body">
        <div class="col-sm-12">
            <form>
                <div class="col-sm-3"><input name="data_inicio" class="form-control col-sm-2" type="date" /></div>
                <div class="col-sm-3"><input name="data_fim" class="form-control col-sm-2" type="date" /></div>
                <div class="col-sm-2"><input type="submit" class="btn btn-primary" value="Gerar" /></div>
            </form>
            <div class="col-sm-12">

                <h3>Receita/Despesa - {{App\Models\Despesa::formatData($dados["dataI"])}} {{App\Models\Despesa::formatData($dados["dataF"])}}</h3>
                <table class="table">
                    <tr>
                        <td>Receita</td>
                        <td>R$ {{App\Models\Despesa::formatValor($receita)}}</td>
                        <td>Despesa</td>
                        <td>R$ {{App\Models\Despesa::formatValor($despesa)}}</td>
                        <td>Lucro</td>
                        <td>R$ {{App\Models\Despesa::formatValor($receita-$despesa)}}</td>
                    </tr>
                </table>
                <br />
                <br />
                <hr>
                <div id="columnchart_material" style="width: 500px; height: 500px;"></div>
            </div>
        </div>
    </div>
    </div>
</div>
@stop
