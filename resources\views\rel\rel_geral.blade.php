@extends('template.master')
@section('sidebar')


@stop
@section('content')
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
    <script type="text/javascript">
        google.charts.load('current', {'packages':['corechart', 'bar']});
        google.charts.setOnLoadCallback(drawChart);
        function drawChart() {

            var data = google.visualization.arrayToDataTable([
                ['Receita', 'Valor'],
                ['Pago',     {{$dados["receitas_total"]}}],
                ['À pagar',  {{$dados["receitas_total_apagar"]}}],
            ]);

            var options = {
                title: 'Receitas do mês de {{date("M/Y")}}',
                slices: {
                    0: { color: 'green' },
                    1: { color: '#F4B400' }
                },
                pieSliceTextStyle: {
                    color: 'black',
                },
                legend: 'none'
            };

            var chart = new google.visualization.PieChart(document.getElementById('piechartMes'));

            chart.draw(data, options);
        }
    </script>


<div class="panel panel-default">
    <div class="panel-heading">Informações</div>
    <div class="panel-body">
        <div class="col-sm-6">
            <h3>Despesas</h3>
            <table class="table">
                <tr>
                    <td><p class="text-danger"><strong>Despesas atrasadas:</strong> {{ $dados["atrasados"]}}</p></td>
                    <td><strong>Total:</strong> R$ {{ App\Models\Despesa::formatValor($dados["atrasados_total"]) }}</td>
                </tr>
                <tr>
                    <td><p class="text-danger"><strong>Despesas do mês à pagar:</strong> {{ $dados["despesas"]}}</p></td>
                    <td><strong>Total:</strong> R$ {{ App\Models\Despesa::formatValor($dados["despesas_total"]) }}</td>
                </tr>
            </table>
        </div>
        <div class="col-sm-6">
            <h3>Receitas (Total do ano: R$ {{App\Models\Despesa::formatValor($dados["valor_total_receita_ano"]) }})</h3>
            <table class="table">
                <tr>
                    <td><p class="text-danger"><strong>Receitas à baixar:</strong> {{ $dados["receitas_atrasadas"]}} O.S.</p></td>
                    <td>
                        <strong>Total:</strong> R$ {{ App\Models\Despesa::formatValor($dados["receitas_atrasadas_total"]) }}<br />
                        <strong>Baixadas este mês:</strong> R$ {{ App\Models\Despesa::formatValor($dados["receitas_total_baixado_mes"]) }}</td>
                </tr>
                <tr>
                    <td><p class="text-success"><strong>Receitas do mês:</strong> {{$dados["receitas"]}} O.S.</p></td>
                    <td><strong>Total:</strong> R$ {{ App\Models\Despesa::formatValor($dados["receitas_total"] + $dados["receitas_total_apagar"]) }}<br /> Pago: R$ {{ App\Models\Despesa::formatValor($dados["receitas_total"]) }} <br />À pagar: R$ {{ App\Models\Despesa::formatValor($dados["receitas_total_apagar"]) }}</td>
                </tr>

                <tr>
                    <td><p class="text-success"><strong>Receitas do dia:</strong> {{$dados["receitas_dia"]}} O.S.</p></td>
                    <td><strong>Total:</strong> R$  {{ App\Models\Despesa::formatValor($dados["receitas_total_dia"] + $dados["receitas_total_apagar_dia"]) }} <br /> Pago: R$ R$ {{ App\Models\Despesa::formatValor($dados["receitas_total_dia"])}} <br />À pagar: R$ {{ App\Models\Despesa::formatValor($dados["receitas_total_apagar_dia"]) }}</td>
                </tr>
            </table>
        </div>
        <div class="col-sm-12">
            <hr>
            <h3>Gráfico de receitas mensais</h3>

            <div id="piechartMes" style="width: 100%; height: 600px;"></div>
        </div>


    </div>
</div>
@stop
