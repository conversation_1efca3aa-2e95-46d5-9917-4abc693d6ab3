table input {
    width: 100px !important;
}
input {
}

    .filtros-container {
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    }

    .filtros-titulo {
        color: #374151;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 2px solid #f3f4f6;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .filtros-titulo i {
        color: #6b7280;
    }

    .form-row-custom {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        align-items: end;
    }

    .form-group-filtro {
        display: flex;
        flex-direction: column;
    }

    .form-group-filtro label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        font-size: 14px;
        color: #374151;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .form-group-filtro label i {
        color: #6b7280;
        font-size: 13px;
    }

    .form-control-filtro {
        width: 100%;
        height: 40px;
        padding: 0 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        background: #ffffff;
        font-size: 14px;
        color: #374151;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .form-control-filtro:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-control-filtro::placeholder {
        color: #9ca3af;
    }

    .periodo-grupo {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        gap: 8px;
        align-items: center;
    }

    .periodo-ate {
        color: #6b7280;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
    }

    .btn-pesquisar-custom {
        background: #3b82f6;
        border: 1px solid #3b82f6;
        color: white;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 6px;
        justify-content: center;
    }

    .btn-pesquisar-custom:hover {
        background: #2563eb;
        border-color: #2563eb;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .btn-pesquisar-custom:active {
        transform: translateY(1px);
    }

    .form-actions {
        grid-column: 1 / -1;
        display: flex;
        justify-content: flex-end;
        margin-top: 8px;
    }

    @media (max-width: 768px) {
        .form-row-custom {
            grid-template-columns: 1fr;
        }

        .periodo-grupo {
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .periodo-ate {
            display: none;
        }

        .form-actions {
            justify-content: stretch;
        }

        .btn-pesquisar-custom {
            width: 100%;
        }
    }

    @media (min-width: 1200px) {
        .form-row-custom {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    /* Estilo para os links de ordenação separados */
    .sort-links {
        display: inline-block;
    }

    .sort-links a {
        color: #337ab7;
        text-decoration: none;
        padding: 2px 4px;
        border-radius: 3px;
        transition: background-color 0.2s ease;
    }

    .sort-links a:hover {
        background-color: #f5f5f5;
        text-decoration: none;
    }

    .sort-separator {
        color: #666;
        margin: 0 4px;
        font-weight: normal;
    }

    /* Estilos para os botões de atalho */
    .atalhos-grupo {
        display: flex;
        flex-direction: row;
        gap: 8px;
        margin-top: 8px;
        justify-content: flex-start;
    }

    .btn-atalho {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #495057;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: center;
        white-space: nowrap;
    }

    .btn-atalho:hover {
        background: #e9ecef;
        border-color: #adb5bd;
        color: #212529;
    }

    .btn-atalho:active {
        background: #dee2e6;
        transform: translateY(1px);
    }

    @media (max-width: 768px) {
        .atalhos-grupo {
            flex-wrap: wrap;
            gap: 6px;
        }

        .btn-atalho {
            font-size: 11px;
            padding: 5px 8px;
        }
    }