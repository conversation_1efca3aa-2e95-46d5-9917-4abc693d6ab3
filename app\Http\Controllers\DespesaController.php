<?php

namespace App\Http\Controllers;

use App\Models\Despesa;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class DespesaController extends Controller
{

    /*
	|--------------------------------------------------------------------------
	| Default Home Controller
	|--------------------------------------------------------------------------
	|
	| You may wish to use controllers instead of, or in addition to, Closure
	| based routes. That's great! Here is an example controller method to
	| get you started. To route to this controller, just add the route:
	|
	|	Route::get('/', 'HomeController@showWelcome');
	|
	*/

    public function Index(Request $request)
    {
        $usuario = ($request->session()->get("logado"));
        if ($request->has('search')) {
            $os = Despesa::where('descricao', 'Like', '%' . $request->input('search') . '%')->orderBy('dt_vencimento', 'desc');
        } else {
            $os = Despesa::orderBy('dt_vencimento', 'desc');
        }

        $os = $os->where("id_empresa", $usuario["id_empresa"])->paginate(15);
        return response()->view('despesa.index', ['os' => $os]);
    }

    public function Add(Request $request)
    {
        return response()->view('despesa.add');
    }
    public function AddAction(Request $request)
    {
        $rules = array(
            "dt_vencimento" => "required",
            "descricao" => "required",
            "valor" => "required",
            "situacao" => "required"
        );

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->route('add')->withErrors($validator)->withInput();
        } else {
            $usuario = ($request->session()->get("logado"));
            $registro = new Despesa();
            $registro->id_empresa = $usuario["id_empresa"];
            $registro->dt_vencimento = implode("-", array_reverse(explode("/", $request->input('dt_vencimento'))));
            $registro->descricao = $request->input('descricao');
            $registro->valor = str_replace(",", ".", str_replace(".", "", $request->input('valor')));
            $registro->situacao = $request->input('situacao');

            $registro->save();

            return redirect()->route('despesas.index');
        }
    }
    public function Edit(Request $request, $id)
    {
        $usuario = ($request->session()->get("logado"));

        $despesa = Despesa::find($id);
        if ($usuario["id_empresa"] == $despesa->id_empresa) {
            return response()->view('despesa.edit', ['despesa' => $despesa]);
        } else {
            return redirect()->route('403');
        }
    }
    public function EditAction(Request $request, $id)
    {
        $rules = array(
            "dt_vencimento" => "required",
            "descricao" => "required",
            "valor" => "required",
            "situacao" => "required"
        );

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->route('despesas.edit', array('id' => $id))->withErrors($validator)->withInput();
        } else {

            $registro = Despesa::find($id);
            $registro->dt_vencimento = implode("-", array_reverse(explode("/", $request->input('dt_vencimento'))));
            $registro->descricao = $request->input('descricao');
            $registro->valor = str_replace(",", ".", str_replace(".", "", $request->input('valor')));
            $registro->situacao = $request->input('situacao');

            $registro->save();

            return redirect()->route('despesas.index');
        }
    }
    public function Delete(Request $request, $id)
    {
        $usuario = ($request->session()->get("logado"));

        $registro = Despesa::find($id);
        if ($usuario["id_empresa"] == $registro->id_empresa) {
            $registro->delete();
            return redirect()->route('despesas.index');
        } else {
            return redirect()->route('403');
        }
    }
}
