<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OticaOS extends Model {

	protected $table = 'oticasos';

    // Relacionamento com Filial
    public function filial()
    {
        return $this->belongsTo(Filial::class, 'id_filial');
    }

    public function getDtemissaoAttribute($value)
    {
        if (empty($value)) return '';
        // Se já está no formato Y-m-d, converte para d/m/Y para exibição
        $data = implode("/",array_reverse(explode("-",$value)));
        return ($data);
    }
    public function getDtpreventregaAttribute($value)
    {
        
        if (empty($value)) return '';
        // Se já está no formato Y-m-d, converte para d/m/Y para exibição
        $data = implode("/",array_reverse(explode("-",$value)));
        return ($data);
    }


    public function getNascimentoAttribute($value)
    {
        $data = implode("/",array_reverse(explode("-",$value)));
        //return "10-10-1000";
        return ($data);
    }

    public function getValorTotalAttribute($value)
    {
        $data = number_format($value, 2, ',', '.');
        return ($data);
    }
    public function getApagarAttribute($value)
    {
        $data = number_format($value, 2, ',', '.');
        return ($data);
    }
    public function getPagoAttribute($value)
    {
        $data = number_format($value, 2, ',', '.');
        return ($data);
    }
    public function getBaixaValorAttribute($value)
    {
        $data = number_format($value, 2, ',', '.');
        return ($data);
    }

    public function getBaixaDataAttribute($value)
    {
        if (empty($value)) return '';
        // Se já está no formato Y-m-d, converte para d/m/Y para exibição
        $data = implode("/",array_reverse(explode("-",$value)));
        return ($data);
    }

    public function getConfirmadoLabAttribute($value)
    {
        if (empty($value)) return '';
        // Se já está no formato Y-m-d, converte para d/m/Y para exibição
        $data = implode("/",array_reverse(explode("-",$value)));
        return ($data);
    }
}
